import databaseService from '../services/databaseService.js';

// Quantities provided by user in order
const newQuantities = [
  242, 644, 22, 17, 4, 7, 16, 1577, 13, 26, 21, 16, 13, 11, 14, 16, 3, -1, 1, -2,
  17, 12, 301, 0, 16, 16, 14, 40, 15, 12, 13, 4, 5, 6, 23, 17, 16, 16, 9, 9,
  34, 15, 15, 25, 5, 10, 4, 15, 0, 0, 0, 0, 1, 3, 3, 3, 0, 0, 0, 0, 0, 2,
  0, 0, 2, 3, 0, 3, 0, 0, 0, 25
];

// Product names in order (matching the initialProducts array)
const productNames = [
  "Survana Rosemary",
  "O. HS (New)", 
  "O. Argireline Solution",
  "O. Ascorbic Acid",
  "O. Ascorbyl Glucoside Solution",
  "O. Niacinamide",
  "O. Retinol 1%",
  "O. Old Hair Serum",
  "O. GA 240 ml",
  "O. GA 100 ml",
  "O. SA 2%",
  "O. Alpha Arbutin 2%",
  "O. Eye Serum",
  "O. Caffine Solution",
  "O. Sunscreen SPF 30",
  "LR-Mela B3",
  "LR-FS",
  "LR-RT",
  "LR-C",
  "Anua Niacinamide",
  "Anua Niacinamide New",
  "Anua Heartleaf Cleansing Oil",
  "Dr. Althea 345 Relief Cream",
  "Bioderma Facewash 200ml",
  "I'm From Rice Toner",
  "I'm From Rice Cream",
  "ROC Glow Serum",
  "Paula's Choice 118 ml",
  "Mielle Rosemary Oil",
  "Beauty of Joseon Rice Sunscreen",
  "Beauty of Joseon Glow Serum",
  "Beauty of Joseon Sun Stick",
  "Some BY MI Vitamin C",
  "Glutathione",
  "Cerave Night Cream",
  "Cerave Acnee Control Cleanser",
  "Cerave Moisturizer",
  "Cerave Foaming Cleanser",
  "Cerave Resurfacing Retinol",
  "Cerave Skin Renewing Retinol",
  "Cerave Skin Renewing Nightly Treatment",
  "COSRX Gentle Cleanser (Red)",
  "COSRX Aloe Sunscreen",
  "Christian Sunscreen 70ml",
  "Cotton Pad",
  "SKIN1004 Ampoule",
  "MISSHA Sun Milk",
  "EltaMD Sunsceeen SPF 46",
  "O. HS 30ml",
  "Mon Paris",
  "Vampire Blood",
  "Tam Dao",
  "Gucci OUD",
  "Tom Ford Tobacco",
  "Mont Blanc",
  "Hugo Boss",
  "Giorgio Armani",
  "Paco Rabanne",
  "Dunhill Desire",
  "Versace Eros",
  "Tommy Girl",
  "Dior Sauvage",
  "Nautica Voyage",
  "Gucci flora Gorgeous gardenia",
  "Good Girl by carolina herrera",
  "Davidoff cool water",
  "Dolce & gabbana the one",
  "creed aventus",
  "one million",
  "BLVGARI Men in Black",
  "Armani Code",
  "Blue de channel paris"
];

export const updateInventoryQuantities = () => {
  console.log('Starting inventory update...');
  
  const allProducts = databaseService.getAllProducts();
  let updatedCount = 0;
  let errors = [];

  // Create a map of product names to their IDs for faster lookup
  const productMap = {};
  allProducts.forEach(product => {
    productMap[product.name] = product.id;
  });

  // Update each product with new quantity
  productNames.forEach((productName, index) => {
    if (index < newQuantities.length) {
      const newQuantity = newQuantities[index];
      const productId = productMap[productName];
      
      if (productId) {
        try {
          // Handle negative quantities (treat as 0)
          const finalQuantity = Math.max(0, newQuantity);
          
          databaseService.updateProduct(productId, { 
            quantity: finalQuantity 
          });
          
          console.log(`Updated ${productName}: ${newQuantity} -> ${finalQuantity}`);
          updatedCount++;
          
          if (newQuantity < 0) {
            console.warn(`Warning: ${productName} had negative quantity (${newQuantity}), set to 0`);
          }
        } catch (error) {
          const errorMsg = `Failed to update ${productName}: ${error.message}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      } else {
        const errorMsg = `Product not found: ${productName}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }
  });

  console.log(`Inventory update completed!`);
  console.log(`Successfully updated: ${updatedCount} products`);
  
  if (errors.length > 0) {
    console.log(`Errors encountered: ${errors.length}`);
    errors.forEach(error => console.error(error));
  }

  return {
    success: errors.length === 0,
    updatedCount,
    errors,
    totalProducts: productNames.length
  };
};

// Function to get current inventory summary
export const getInventorySummary = () => {
  const allProducts = databaseService.getAllProducts();
  
  const summary = {
    totalProducts: allProducts.length,
    totalQuantity: allProducts.reduce((sum, product) => sum + product.quantity, 0),
    outOfStock: allProducts.filter(p => p.quantity === 0).length,
    lowStock: allProducts.filter(p => p.quantity > 0 && p.quantity <= 5).length,
    inStock: allProducts.filter(p => p.quantity > 5).length,
    highestStock: allProducts.reduce((max, product) => 
      product.quantity > max.quantity ? product : max, allProducts[0] || { quantity: 0 }),
    categoryBreakdown: {}
  };

  // Calculate category breakdown
  allProducts.forEach(product => {
    const category = product.category || 'Uncategorized';
    if (!summary.categoryBreakdown[category]) {
      summary.categoryBreakdown[category] = {
        count: 0,
        totalQuantity: 0
      };
    }
    summary.categoryBreakdown[category].count++;
    summary.categoryBreakdown[category].totalQuantity += product.quantity;
  });

  return summary;
};
