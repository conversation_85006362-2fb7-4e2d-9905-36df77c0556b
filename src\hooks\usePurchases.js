import { useState, useEffect } from 'react';
import purchaseService from '../services/purchaseService.js';

export const usePurchases = () => {
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadPurchases = async () => {
    try {
      setLoading(true);
      setError(null);
      const purchaseData = purchaseService.getAllPurchases();
      // Sort by date (newest first)
      const sortedPurchases = purchaseData.sort((a, b) => new Date(b.date) - new Date(a.date));
      setPurchases(sortedPurchases);
    } catch (err) {
      console.error('Error loading purchases:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createPurchase = async (purchaseData) => {
    try {
      const newPurchase = await purchaseService.createPurchase(purchaseData);
      await loadPurchases(); // Reload to get updated data
      return newPurchase;
    } catch (err) {
      console.error('Error creating purchase:', err);
      setError(err.message);
      throw err;
    }
  };

  const updatePurchase = async (id, updates) => {
    try {
      const updatedPurchase = await purchaseService.updatePurchase(id, updates);
      await loadPurchases(); // Reload to get updated data
      return updatedPurchase;
    } catch (err) {
      console.error('Error updating purchase:', err);
      setError(err.message);
      throw err;
    }
  };

  const deletePurchase = async (id) => {
    try {
      await purchaseService.deletePurchase(id);
      await loadPurchases(); // Reload to get updated data
    } catch (err) {
      console.error('Error deleting purchase:', err);
      setError(err.message);
      throw err;
    }
  };

  const searchPurchases = (query) => {
    try {
      const results = purchaseService.searchPurchases(query);
      return results.sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (err) {
      console.error('Error searching purchases:', err);
      setError(err.message);
      return [];
    }
  };

  const filterPurchasesByDateRange = (startDate, endDate) => {
    try {
      const results = purchaseService.filterPurchasesByDateRange(startDate, endDate);
      return results.sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (err) {
      console.error('Error filtering purchases:', err);
      setError(err.message);
      return [];
    }
  };

  const getPurchaseStats = () => {
    try {
      return purchaseService.getPurchaseStats();
    } catch (err) {
      console.error('Error getting purchase stats:', err);
      setError(err.message);
      return {
        totalPurchases: 0,
        totalAmount: 0,
        totalQuantity: 0,
        averageOrderValue: 0,
        purchasesByMonth: {},
        topItems: {},
        recentPurchases: []
      };
    }
  };

  const getInventoryValue = () => {
    try {
      return purchaseService.getInventoryValue();
    } catch (err) {
      console.error('Error getting inventory value:', err);
      setError(err.message);
      return 0;
    }
  };

  // Load purchases on mount
  useEffect(() => {
    loadPurchases();
  }, []);

  // Listen for purchase events
  useEffect(() => {
    const handlePurchaseCreated = () => loadPurchases();
    const handlePurchaseUpdated = () => loadPurchases();
    const handlePurchaseDeleted = () => loadPurchases();

    window.addEventListener('purchaseCreated', handlePurchaseCreated);
    window.addEventListener('purchaseUpdated', handlePurchaseUpdated);
    window.addEventListener('purchaseDeleted', handlePurchaseDeleted);

    return () => {
      window.removeEventListener('purchaseCreated', handlePurchaseCreated);
      window.removeEventListener('purchaseUpdated', handlePurchaseUpdated);
      window.removeEventListener('purchaseDeleted', handlePurchaseDeleted);
    };
  }, []);

  return {
    purchases,
    loading,
    error,
    createPurchase,
    updatePurchase,
    deletePurchase,
    searchPurchases,
    filterPurchasesByDateRange,
    getPurchaseStats,
    getInventoryValue,
    refreshPurchases: loadPurchases
  };
};
