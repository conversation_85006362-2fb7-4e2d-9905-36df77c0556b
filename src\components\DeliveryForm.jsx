import React, { useState, useEffect } from 'react';
import { useProducts } from '../hooks/useProducts.js';
import deliveryService from '../services/deliveryService.js';
import './DeliveryForm.css';

const DeliveryForm = ({ isOpen, onClose, onDeliveryCreated, editingDelivery = null }) => {
  const { products } = useProducts();
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    customerInfo: {
      name: '',
      phone: '',
      address: ''
    },
    items: [{ productId: '', quantity: 1 }],
    status: 'dispatched',
    notes: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (editingDelivery) {
        setFormData({
          date: editingDelivery.date,
          customerInfo: editingDelivery.customerInfo || { name: '', phone: '', address: '' },
          items: editingDelivery.items || [{ productId: '', quantity: 1 }],
          status: editingDelivery.status || 'dispatched',
          notes: editingDelivery.notes || ''
        });
      } else {
        setFormData({
          date: new Date().toISOString().split('T')[0],
          customerInfo: { name: '', phone: '', address: '' },
          items: [{ productId: '', quantity: 1 }],
          status: 'dispatched',
          notes: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, editingDelivery]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('customer.')) {
      const customerField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        customerInfo: {
          ...prev.customerInfo,
          [customerField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      [field]: field === 'quantity' ? parseInt(value) || 0 : value
    };

    // Add product name for display
    if (field === 'productId' && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        newItems[index].productName = product.name;
        newItems[index].availableStock = product.quantity;
      }
    }

    setFormData(prev => ({
      ...prev,
      items: newItems
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', quantity: 1 }]
    }));
  };

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index)
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = 'Date is required';
    }

    if (formData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    formData.items.forEach((item, index) => {
      if (!item.productId) {
        newErrors[`item_${index}_product`] = 'Product is required';
      }
      if (!item.quantity || item.quantity <= 0) {
        newErrors[`item_${index}_quantity`] = 'Valid quantity is required';
      }
      
      // Check stock availability
      if (item.productId && item.quantity) {
        const product = products.find(p => p.id === item.productId);
        if (product && product.quantity < item.quantity) {
          newErrors[`item_${index}_quantity`] = `Only ${product.quantity} available`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare items with product names
      const itemsWithNames = formData.items.map(item => {
        const product = products.find(p => p.id === item.productId);
        return {
          ...item,
          productName: product ? product.name : 'Unknown Product'
        };
      });

      const deliveryData = {
        ...formData,
        items: itemsWithNames
      };

      let result;
      if (editingDelivery) {
        result = deliveryService.updateDelivery(editingDelivery.id, deliveryData);
      } else {
        result = deliveryService.createDelivery(deliveryData);
      }

      onDeliveryCreated(result);
      onClose();
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content delivery-form-modal">
        <div className="modal-header">
          <h2>{editingDelivery ? '📝 Edit Delivery' : '📦 New Delivery'}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="delivery-form">
          {/* Date and Status */}
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="date">Delivery Date *</label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className={errors.date ? 'error' : ''}
                required
              />
              {errors.date && <span className="error-text">{errors.date}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
              >
                <option value="dispatched">📤 Dispatched</option>
                <option value="delivered">✅ Delivered</option>
                <option value="returned">↩️ Returned</option>
              </select>
            </div>
          </div>

          {/* Customer Information */}
          <div className="form-section">
            <h3>👤 Customer Information</h3>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="customerName">Customer Name</label>
                <input
                  type="text"
                  id="customerName"
                  name="customer.name"
                  value={formData.customerInfo.name}
                  onChange={handleInputChange}
                  placeholder="Enter customer name"
                />
              </div>

              <div className="form-group">
                <label htmlFor="customerPhone">Phone Number</label>
                <input
                  type="tel"
                  id="customerPhone"
                  name="customer.phone"
                  value={formData.customerInfo.phone}
                  onChange={handleInputChange}
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="customerAddress">Address</label>
              <textarea
                id="customerAddress"
                name="customer.address"
                value={formData.customerInfo.address}
                onChange={handleInputChange}
                placeholder="Enter delivery address"
                rows="2"
              />
            </div>
          </div>

          {/* Items */}
          <div className="form-section">
            <div className="section-header">
              <h3>📋 Items to Dispatch</h3>
              <button type="button" className="btn btn-secondary" onClick={addItem}>
                + Add Item
              </button>
            </div>

            {formData.items.map((item, index) => {
              const product = products.find(p => p.id === item.productId);
              return (
                <div key={index} className="item-row">
                  <div className="form-group flex-2">
                    <label>Product *</label>
                    <select
                      value={item.productId}
                      onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                      className={errors[`item_${index}_product`] ? 'error' : ''}
                      required
                    >
                      <option value="">Select a product</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} (Stock: {product.quantity})
                        </option>
                      ))}
                    </select>
                    {errors[`item_${index}_product`] && (
                      <span className="error-text">{errors[`item_${index}_product`]}</span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Quantity *</label>
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                      className={errors[`item_${index}_quantity`] ? 'error' : ''}
                      min="1"
                      max={product ? product.quantity : 999}
                      required
                    />
                    {errors[`item_${index}_quantity`] && (
                      <span className="error-text">{errors[`item_${index}_quantity`]}</span>
                    )}
                  </div>

                  {formData.items.length > 1 && (
                    <button
                      type="button"
                      className="btn btn-danger remove-item-btn"
                      onClick={() => removeItem(index)}
                    >
                      🗑️
                    </button>
                  )}
                </div>
              );
            })}
            {errors.items && <span className="error-text">{errors.items}</span>}
          </div>

          {/* Notes */}
          <div className="form-group">
            <label htmlFor="notes">Notes</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              placeholder="Additional notes about this delivery..."
              rows="3"
            />
          </div>

          {/* Submit Buttons */}
          <div className="form-actions">
            {errors.submit && <div className="error-text">{errors.submit}</div>}
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : (editingDelivery ? 'Update Delivery' : 'Create Delivery')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DeliveryForm;
