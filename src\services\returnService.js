import databaseService from './databaseService.js';

const RETURN_STORAGE_KEY = 'inventory_returns';

class ReturnService {
  constructor() {
    this.initializeReturns();
  }

  // Initialize return storage if it doesn't exist
  initializeReturns() {
    const existingData = localStorage.getItem(RETURN_STORAGE_KEY);
    if (!existingData) {
      const initialData = {
        returns: [],
        metadata: {
          lastUpdated: new Date().toISOString(),
          totalReturns: 0,
          nextReturnId: 1
        }
      };
      localStorage.setItem(RETURN_STORAGE_KEY, JSON.stringify(initialData));
    }
  }

  // Get all returns
  getAllReturns() {
    try {
      const data = JSON.parse(localStorage.getItem(RETURN_STORAGE_KEY));
      return data.returns || [];
    } catch (error) {
      console.error('Error reading returns:', error);
      return [];
    }
  }

  // Get return by ID
  getReturnById(id) {
    const returns = this.getAllReturns();
    return returns.find(returnItem => returnItem.id === id);
  }

  // Get returns by order ID
  getReturnsByOrderId(orderId) {
    const returns = this.getAllReturns();
    return returns.filter(returnItem => returnItem.orderId === orderId);
  }

  // Get returns by date range
  getReturnsByDateRange(startDate, endDate) {
    const returns = this.getAllReturns();
    return returns.filter(returnItem => {
      const returnDate = new Date(returnItem.date);
      return returnDate >= new Date(startDate) && returnDate <= new Date(endDate);
    });
  }

  // Create new return
  createReturn(returnData) {
    try {
      const data = JSON.parse(localStorage.getItem(RETURN_STORAGE_KEY));
      
      // Validate return data
      this.validateReturnData(returnData);

      // Create return object
      const newReturn = {
        id: data.metadata.nextReturnId,
        orderId: returnData.orderId,
        date: returnData.date,
        items: returnData.items || [],
        reason: returnData.reason || '',
        status: returnData.status || 'received',
        totalItems: returnData.items.reduce((sum, item) => sum + item.quantity, 0),
        notes: returnData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to returns array
      data.returns.push(newReturn);
      
      // Update metadata
      data.metadata.nextReturnId++;
      data.metadata.totalReturns++;
      data.metadata.lastUpdated = new Date().toISOString();

      // Save to localStorage
      localStorage.setItem(RETURN_STORAGE_KEY, JSON.stringify(data));

      // Update inventory for received returns
      if (newReturn.status === 'received') {
        this.updateInventoryForReturn(newReturn.items, 'add');
      }

      // Dispatch custom event for inventory updates
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));

      return newReturn;
    } catch (error) {
      console.error('Error creating return:', error);
      throw error;
    }
  }

  // Update return
  updateReturn(returnId, updates) {
    try {
      const data = JSON.parse(localStorage.getItem(RETURN_STORAGE_KEY));
      const returnIndex = data.returns.findIndex(r => r.id === returnId);
      
      if (returnIndex === -1) {
        throw new Error('Return not found');
      }

      const oldReturn = { ...data.returns[returnIndex] };
      const updatedReturn = {
        ...oldReturn,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      // Handle status changes that affect inventory
      if (oldReturn.status !== updatedReturn.status) {
        this.handleStatusChange(oldReturn, updatedReturn);
      }

      data.returns[returnIndex] = updatedReturn;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(RETURN_STORAGE_KEY, JSON.stringify(data));

      // Dispatch custom event for inventory updates
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));

      return updatedReturn;
    } catch (error) {
      console.error('Error updating return:', error);
      throw error;
    }
  }

  // Delete return
  deleteReturn(returnId) {
    try {
      const data = JSON.parse(localStorage.getItem(RETURN_STORAGE_KEY));
      const returnIndex = data.returns.findIndex(r => r.id === returnId);
      
      if (returnIndex === -1) {
        throw new Error('Return not found');
      }

      const returnItem = data.returns[returnIndex];
      
      // Remove inventory if return was received
      if (returnItem.status === 'received') {
        this.updateInventoryForReturn(returnItem.items, 'subtract');
      }

      data.returns.splice(returnIndex, 1);
      data.metadata.totalReturns--;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(RETURN_STORAGE_KEY, JSON.stringify(data));

      // Dispatch custom event for inventory updates
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));

      return true;
    } catch (error) {
      console.error('Error deleting return:', error);
      throw error;
    }
  }

  // Update inventory based on return items
  updateInventoryForReturn(items, operation) {
    items.forEach(item => {
      try {
        const product = databaseService.getProductById(item.productId);
        if (product) {
          let newQuantity;
          if (operation === 'add') {
            newQuantity = product.quantity + item.quantity;
          } else if (operation === 'subtract') {
            newQuantity = Math.max(0, product.quantity - item.quantity);
          }
          
          databaseService.updateProduct(item.productId, { quantity: newQuantity });
        }
      } catch (error) {
        console.error(`Error updating inventory for product ${item.productId}:`, error);
      }
    });
  }

  // Handle return status changes
  handleStatusChange(oldReturn, newReturn) {
    const oldStatus = oldReturn.status;
    const newStatus = newReturn.status;

    // If changing from pending to received, add to inventory
    if (oldStatus === 'pending' && newStatus === 'received') {
      this.updateInventoryForReturn(newReturn.items, 'add');
    }
    
    // If changing from received to pending, subtract from inventory
    if (oldStatus === 'received' && newStatus === 'pending') {
      this.updateInventoryForReturn(oldReturn.items, 'subtract');
    }
  }

  // Validate return data
  validateReturnData(returnData) {
    if (!returnData.orderId || returnData.orderId.trim() === '') {
      throw new Error('Order ID is required');
    }
    
    if (!returnData.date) {
      throw new Error('Return date is required');
    }

    if (!returnData.items || returnData.items.length === 0) {
      throw new Error('At least one item is required');
    }

    // Validate each item
    returnData.items.forEach((item, index) => {
      if (!item.productId) {
        throw new Error(`Product ID is required for item ${index + 1}`);
      }
      if (!item.quantity || item.quantity <= 0) {
        throw new Error(`Valid quantity is required for item ${index + 1}`);
      }
      
      // Check if product exists
      const product = databaseService.getProductById(item.productId);
      if (!product) {
        throw new Error(`Product not found for item ${index + 1}`);
      }
    });
  }

  // Get return statistics
  getReturnStats() {
    const returns = this.getAllReturns();
    
    const stats = {
      totalReturns: returns.length,
      totalItemsReturned: 0,
      returnsByStatus: {
        received: 0,
        pending: 0,
        processed: 0
      },
      returnsByDate: {},
      returnsByReason: {},
      recentReturns: returns
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
    };

    returns.forEach(returnItem => {
      stats.totalItemsReturned += returnItem.totalItems;
      stats.returnsByStatus[returnItem.status]++;
      
      const dateKey = new Date(returnItem.date).toDateString();
      if (!stats.returnsByDate[dateKey]) {
        stats.returnsByDate[dateKey] = 0;
      }
      stats.returnsByDate[dateKey]++;

      const reason = returnItem.reason || 'No reason specified';
      if (!stats.returnsByReason[reason]) {
        stats.returnsByReason[reason] = 0;
      }
      stats.returnsByReason[reason]++;
    });

    return stats;
  }

  // Search returns
  searchReturns(query) {
    const returns = this.getAllReturns();
    const searchTerm = query.toLowerCase();

    return returns.filter(returnItem => {
      return (
        returnItem.orderId?.toLowerCase().includes(searchTerm) ||
        returnItem.reason?.toLowerCase().includes(searchTerm) ||
        returnItem.notes?.toLowerCase().includes(searchTerm) ||
        returnItem.items.some(item =>
          item.productName?.toLowerCase().includes(searchTerm)
        )
      );
    });
  }

  // Get returns by order ID
  getReturnsByOrderId(orderId) {
    const returns = this.getAllReturns();
    return returns.filter(returnItem =>
      returnItem.orderId?.toLowerCase() === orderId.toLowerCase()
    );
  }

  // Check if order ID exists
  orderIdExists(orderId, excludeReturnId = null) {
    const returns = this.getAllReturns();
    return returns.some(returnItem =>
      returnItem.orderId?.toLowerCase() === orderId.toLowerCase() &&
      (!excludeReturnId || returnItem.id !== excludeReturnId)
    );
  }
}

const returnService = new ReturnService();
export default returnService;
