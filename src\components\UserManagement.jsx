import React, { useState } from 'react';
import { useUserManagement, useAuth } from '../hooks/useAuth.js';
import { USER_ROLES } from '../services/authService.js';
import './UserManagement.css';

const UserManagement = () => {
  const { users, loading, error, addUser, updateUser, deleteUser, setError } = useUserManagement();
  const { user: currentUser } = useAuth();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  const handleAddUser = () => {
    setEditingUser(null);
    setShowAddForm(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setShowAddForm(true);
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        await deleteUser(userId);
      } catch (err) {
        console.error('Failed to delete user:', err);
      }
    }
  };

  const handleFormClose = () => {
    setShowAddForm(false);
    setEditingUser(null);
    setError(null);
  };

  const handleFormSubmit = async (userData) => {
    try {
      if (editingUser) {
        await updateUser(editingUser.id, userData);
      } else {
        await addUser(userData);
      }
      handleFormClose();
    } catch (err) {
      console.error('Failed to save user:', err);
    }
  };

  return (
    <div className="user-management">
      <div className="user-management-header">
        <div>
          <h2>User Management</h2>
          <p>Manage system users and their access permissions.</p>
        </div>
        <button className="add-user-button" onClick={handleAddUser}>
          <span className="button-icon">➕</span>
          Add User
        </button>
      </div>

      {error && (
        <div className="error-message">
          <span className="error-icon">⚠️</span>
          {error}
        </div>
      )}

      <div className="users-grid">
        {loading ? (
          <div className="loading-state">
            <span className="loading-spinner">⏳</span>
            Loading users...
          </div>
        ) : users.length === 0 ? (
          <div className="empty-state">
            <span className="empty-icon">👥</span>
            <h3>No users found</h3>
            <p>Add your first user to get started.</p>
          </div>
        ) : (
          users.map(user => (
            <UserCard
              key={user.id}
              user={user}
              currentUser={currentUser}
              onEdit={handleEditUser}
              onDelete={handleDeleteUser}
            />
          ))
        )}
      </div>

      {showAddForm && (
        <UserForm
          user={editingUser}
          onSubmit={handleFormSubmit}
          onClose={handleFormClose}
          error={error}
        />
      )}
    </div>
  );
};

// User Card Component
const UserCard = ({ user, currentUser, onEdit, onDelete }) => {
  const isCurrentUser = currentUser?.id === user.id;

  return (
    <div className={`user-card ${!user.isActive ? 'inactive' : ''}`}>
      <div className="user-avatar">
        <span className="avatar-icon">
          {user.role === USER_ROLES.ADMIN ? '👑' : '👤'}
        </span>
      </div>
      
      <div className="user-info">
        <h3 className="user-name">
          {user.name}
          {isCurrentUser && <span className="current-user-badge">You</span>}
        </h3>
        <p className="user-email">{user.email}</p>
        <p className="user-username">@{user.username}</p>
        
        <div className="user-meta">
          <span className={`role-badge ${user.role}`}>
            {user.role === USER_ROLES.ADMIN ? '👑 Admin' : '👀 Viewer'}
          </span>
          <span className={`status-badge ${user.isActive ? 'active' : 'inactive'}`}>
            {user.isActive ? '✅ Active' : '❌ Inactive'}
          </span>
        </div>
        
        <div className="user-dates">
          <small>Created: {new Date(user.createdAt).toLocaleDateString()}</small>
          {user.updatedAt && (
            <small>Updated: {new Date(user.updatedAt).toLocaleDateString()}</small>
          )}
        </div>
      </div>
      
      <div className="user-actions">
        <button
          className="edit-button"
          onClick={() => onEdit(user)}
          title="Edit User"
        >
          ✏️
        </button>
        {!isCurrentUser && (
          <button
            className="delete-button"
            onClick={() => onDelete(user.id)}
            title="Delete User"
          >
            🗑️
          </button>
        )}
      </div>
    </div>
  );
};

// User Form Component
const UserForm = ({ user, onSubmit, onClose, error }) => {
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    username: user?.username || '',
    password: '',
    role: user?.role || USER_ROLES.VIEWER,
    isActive: user?.isActive !== undefined ? user.isActive : true
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters';
    }
    
    if (!user && !formData.password.trim()) {
      errors.password = 'Password is required for new users';
    } else if (formData.password && formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submitData = { ...formData };
    
    // Don't include password if it's empty for existing users
    if (user && !formData.password.trim()) {
      delete submitData.password;
    }

    onSubmit(submitData);
  };

  return (
    <div className="modal-overlay">
      <div className="user-form-modal">
        <div className="modal-header">
          <h3>{user ? 'Edit User' : 'Add New User'}</h3>
          <button className="close-button" onClick={onClose}>✕</button>
        </div>

        <form onSubmit={handleSubmit} className="user-form">
          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={formErrors.name ? 'error' : ''}
                placeholder="Enter full name"
                required
              />
              {formErrors.name && (
                <span className="field-error">{formErrors.name}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={formErrors.email ? 'error' : ''}
                placeholder="Enter email address"
                required
              />
              {formErrors.email && (
                <span className="field-error">{formErrors.email}</span>
              )}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="username">Username *</label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                className={formErrors.username ? 'error' : ''}
                placeholder="Enter username"
                required
              />
              {formErrors.username && (
                <span className="field-error">{formErrors.username}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="role">Role *</label>
              <select
                id="role"
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                required
              >
                <option value={USER_ROLES.VIEWER}>👀 Viewer (Read-only)</option>
                <option value={USER_ROLES.ADMIN}>👑 Admin (Full access)</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="password">
              Password {user ? '(leave blank to keep current)' : '*'}
            </label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={formErrors.password ? 'error' : ''}
                placeholder={user ? 'Enter new password' : 'Enter password'}
                required={!user}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
            {formErrors.password && (
              <span className="field-error">{formErrors.password}</span>
            )}
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <span className="checkbox-text">Active user account</span>
            </label>
          </div>

          <div className="form-actions">
            <button type="button" className="cancel-button" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="submit-button">
              {user ? 'Update User' : 'Create User'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UserManagement;
