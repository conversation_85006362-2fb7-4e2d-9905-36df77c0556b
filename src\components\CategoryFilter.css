/* Category Filter Styles */
.category-filter {
  min-width: 150px;
}

.category-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.category-select:hover {
  border-color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .category-filter {
    min-width: 100%;
  }
}
