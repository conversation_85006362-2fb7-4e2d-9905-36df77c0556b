import React, { useState } from 'react';
import { updateInventoryQuantities, getInventorySummary } from '../utils/updateInventory.js';
import './Dashboard.css';

const Dashboard = ({ products }) => {
  const [updateStatus, setUpdateStatus] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Handle inventory update
  const handleInventoryUpdate = async () => {
    setIsUpdating(true);
    setUpdateStatus(null);

    try {
      const result = updateInventoryQuantities();
      setUpdateStatus({
        type: result.success ? 'success' : 'warning',
        message: result.success
          ? `Successfully updated ${result.updatedCount} products!`
          : `Updated ${result.updatedCount} products with ${result.errors.length} errors.`,
        details: result
      });

      // Refresh the page to show updated data
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      setUpdateStatus({
        type: 'error',
        message: `Failed to update inventory: ${error.message}`,
        details: null
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Calculate statistics
  const totalProducts = products.length;
  const outOfStock = products.filter(p => p.quantity === 0).length;
  const lowStock = products.filter(p => p.quantity > 0 && p.quantity <= 5).length;
  const inStock = products.filter(p => p.quantity > 5).length;

  // Category breakdown
  const categoryStats = products.reduce((acc, product) => {
    const category = product.category || 'Uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  const topCategories = Object.entries(categoryStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  // Recent products (last 10 added)
  const recentProducts = [...products]
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5);

  // Low stock alerts
  const lowStockProducts = products
    .filter(p => p.quantity <= 5)
    .sort((a, b) => a.quantity - b.quantity);

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>📊 Dashboard Overview</h2>
        <p>Monitor your inventory at a glance</p>

        {/* Inventory Update Section */}
        <div className="update-section">
          <button
            className="btn btn-primary update-btn"
            onClick={handleInventoryUpdate}
            disabled={isUpdating}
          >
            {isUpdating ? '🔄 Updating...' : '📝 Update Inventory Quantities'}
          </button>

          {updateStatus && (
            <div className={`update-status ${updateStatus.type}`}>
              <p>{updateStatus.message}</p>
              {updateStatus.details && updateStatus.details.errors.length > 0 && (
                <details>
                  <summary>View Errors ({updateStatus.details.errors.length})</summary>
                  <ul>
                    {updateStatus.details.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </details>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <h3>{totalProducts}</h3>
            <p>Total Products</p>
          </div>
        </div>

        <div className="stat-card in-stock">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>{inStock}</h3>
            <p>In Stock</p>
          </div>
        </div>

        <div className="stat-card low-stock">
          <div className="stat-icon">⚠️</div>
          <div className="stat-content">
            <h3>{lowStock}</h3>
            <p>Low Stock</p>
          </div>
        </div>

        <div className="stat-card out-of-stock">
          <div className="stat-icon">❌</div>
          <div className="stat-content">
            <h3>{outOfStock}</h3>
            <p>Out of Stock</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Category Breakdown */}
        <div className="dashboard-section">
          <h3>📋 Top Categories</h3>
          <div className="category-list">
            {topCategories.length > 0 ? (
              topCategories.map(([category, count]) => (
                <div key={category} className="category-item">
                  <span className="category-name">{category}</span>
                  <span className="category-count">{count} products</span>
                </div>
              ))
            ) : (
              <p className="empty-message">No categories found</p>
            )}
          </div>
        </div>

        {/* Low Stock Alerts */}
        <div className="dashboard-section">
          <h3>⚠️ Stock Alerts</h3>
          <div className="alert-list">
            {lowStockProducts.length > 0 ? (
              lowStockProducts.slice(0, 5).map(product => (
                <div key={product.id} className="alert-item">
                  <div className="alert-product">
                    <span className="product-name">{product.name}</span>
                    <span className="product-category">{product.category}</span>
                  </div>
                  <span className={`quantity-badge ${product.quantity === 0 ? 'out' : 'low'}`}>
                    {product.quantity === 0 ? 'Out of Stock' : `${product.quantity} left`}
                  </span>
                </div>
              ))
            ) : (
              <p className="empty-message">All products are well stocked! 🎉</p>
            )}
          </div>
        </div>

        {/* Recent Products */}
        <div className="dashboard-section">
          <h3>🆕 Recently Added</h3>
          <div className="recent-list">
            {recentProducts.length > 0 ? (
              recentProducts.map(product => (
                <div key={product.id} className="recent-item">
                  <div className="recent-product">
                    <span className="product-name">{product.name}</span>
                    <span className="product-date">
                      {new Date(product.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <span className="product-quantity">{product.quantity} units</span>
                </div>
              ))
            ) : (
              <p className="empty-message">No products added yet</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
