/* Product List Styles */
.product-list {
  width: 100%;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.product-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.product-header {
  margin-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1rem;
}

.product-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.product-sku {
  font-size: 0.85rem;
  color: #666;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: monospace;
}

.product-details {
  margin-bottom: 1rem;
}

.product-category {
  margin-bottom: 1rem;
}

.category-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.product-quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.quantity-label {
  font-weight: 500;
  color: #555;
}

.quantity-value {
  font-weight: 600;
  font-size: 1.1rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  min-width: 40px;
  text-align: center;
}

.quantity-value.in-stock {
  background: #d4edda;
  color: #155724;
}

.quantity-value.low-stock {
  background: #fff3cd;
  color: #856404;
}

.quantity-value.out-of-stock {
  background: #f8d7da;
  color: #721c24;
}

.stock-status {
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
}

.stock-status.in-stock {
  background: #28a745;
  color: white;
}

.stock-status.low-stock {
  background: #ffc107;
  color: #212529;
}

.stock-status.out-of-stock {
  background: #dc3545;
  color: white;
}

.product-description {
  margin-bottom: 1rem;
}

.product-description p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.product-meta {
  margin-bottom: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.product-meta small {
  display: block;
  color: #888;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.product-actions .btn {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
}

.product-summary {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  padding: 1rem;
  background: white;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: #666;
}

.empty-state p {
  margin: 0;
  color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .product-card {
    padding: 1rem;
  }
  
  .product-actions {
    justify-content: center;
  }
  
  .product-quantity {
    flex-wrap: wrap;
  }
}
