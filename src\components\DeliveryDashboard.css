/* Delivery Dashboard Styles */
.delivery-dashboard {
  padding: 1rem;
  background: #f5f5f5;
  min-height: 100vh;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  font-size: 1.2rem;
  color: #666;
}

.error {
  color: #e74c3c;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.dashboard-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.date-range-selector label {
  color: #555;
}

.date-range-selector input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.date-range-selector span {
  color: #666;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.primary {
  border-left: 4px solid #667eea;
}

.stat-card.success {
  border-left: 4px solid #28a745;
}

.stat-card.info {
  border-left: 4px solid #17a2b8;
}

.stat-card.warning {
  border-left: 4px solid #ffc107;
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 1rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  margin-bottom: 0.25rem;
}

.stat-sublabel {
  font-size: 0.8rem;
  color: #888;
}

/* Status Section */
.status-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.status-card {
  padding: 1rem;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.status-card.dispatched {
  border-left: 4px solid #ffc107;
}

.status-card.delivered {
  border-left: 4px solid #28a745;
}

.status-card.returned {
  border-left: 4px solid #dc3545;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.status-icon {
  font-size: 1.2rem;
}

.status-name {
  font-weight: 600;
  color: #333;
}

.status-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.status-count {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

.status-percentage {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.status-bar {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.status-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.dispatched-fill {
  background: #ffc107;
}

.delivered-fill {
  background: #28a745;
}

.returned-fill {
  background: #dc3545;
}

/* Recent Section */
.recent-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.recent-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.recent-deliveries {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recent-delivery {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.delivery-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.delivery-id {
  font-weight: 600;
  color: #667eea;
}

.delivery-date,
.delivery-items {
  color: #666;
  font-size: 0.9rem;
}

.delivery-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.delivery-status.dispatched {
  background: rgba(255, 243, 205, 0.9);
  color: #856404;
}

.delivery-status.delivered {
  background: rgba(212, 237, 218, 0.9);
  color: #155724;
}

.delivery-status.returned {
  background: rgba(248, 215, 218, 0.9);
  color: #721c24;
}

.no-recent {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}

/* Trend Section */
.trend-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.trend-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.trend-chart {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 150px;
  padding: 1rem 0;
}

.trend-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.trend-fill {
  width: 100%;
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  min-height: 10px;
  transition: height 0.3s ease;
}

.trend-label {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.trend-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin-top: 0.25rem;
}

/* Metrics Section */
.metrics-section {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.metrics-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.3rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.metric {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
  .delivery-dashboard {
    padding: 0.5rem;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .date-range-selector {
    flex-wrap: wrap;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .status-cards {
    grid-template-columns: 1fr;
  }

  .delivery-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .trend-chart {
    gap: 0.5rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
