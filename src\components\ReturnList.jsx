import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth.js';
import './ReturnList.css';

const ReturnList = ({ returns, onEditReturn, onDeleteReturn, onRefresh }) => {
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');

  const { hasPermission } = useAuth();

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const sortedReturns = [...returns].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'date':
        aValue = new Date(a.date);
        bValue = new Date(b.date);
        break;
      case 'orderId':
        aValue = a.orderId.toLowerCase();
        bValue = b.orderId.toLowerCase();
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'totalItems':
        aValue = a.totalItems;
        bValue = b.totalItems;
        break;
      case 'reason':
        aValue = a.reason || '';
        bValue = b.reason || '';
        break;
      default:
        aValue = a.createdAt;
        bValue = b.createdAt;
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      received: { emoji: '✅', label: 'Received', class: 'status-received' },
      pending: { emoji: '⏳', label: 'Pending', class: 'status-pending' },
      processed: { emoji: '📋', label: 'Processed', class: 'status-processed' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`status-badge ${config.class}`}>
        {config.emoji} {config.label}
      </span>
    );
  };

  const getReasonDisplay = (reason) => {
    const reasonMap = {
      defective: 'Defective Product',
      wrong_item: 'Wrong Item Sent',
      damaged: 'Damaged in Transit',
      customer_change: 'Customer Changed Mind',
      quality_issue: 'Quality Issue',
      expired: 'Expired Product',
      other: 'Other'
    };
    return reasonMap[reason] || reason || 'No reason specified';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (returns.length === 0) {
    return (
      <div className="return-list-empty">
        <div className="empty-state">
          <h3>No returns found</h3>
          <p>No returns match your current filters.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="return-list">
      {/* Controls */}
      <div className="return-controls">
        <div className="sort-group">
          <label>Sort by:</label>
          <select 
            value={sortBy} 
            onChange={(e) => handleSort(e.target.value)}
          >
            <option value="date">Return Date</option>
            <option value="orderId">Order ID</option>
            <option value="status">Status</option>
            <option value="totalItems">Total Items</option>
            <option value="reason">Reason</option>
          </select>
          <button 
            className="sort-order-btn"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>

        <div className="results-info">
          {returns.length} return{returns.length !== 1 ? 's' : ''} found
        </div>
      </div>

      {/* Return Cards */}
      <div className="return-cards">
        {sortedReturns.map(returnItem => (
          <div key={returnItem.id} className="return-card">
            <div className="return-card-header">
              <div className="return-info">
                <h3>Order #{returnItem.orderId}</h3>
                <div className="return-meta">
                  <span className="return-date">📅 {formatDate(returnItem.date)}</span>
                  <span className="return-id">ID: {returnItem.id}</span>
                </div>
              </div>
              <div className="return-status">
                {getStatusBadge(returnItem.status)}
              </div>
            </div>

            <div className="return-card-body">
              {/* Items */}
              <div className="return-items">
                <h4>📦 Items ({returnItem.totalItems} total)</h4>
                <div className="items-list">
                  {returnItem.items.map((item, index) => (
                    <div key={index} className="item-entry">
                      <span className="item-name">{item.productName}</span>
                      <span className="item-quantity">×{item.quantity}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Reason */}
              {returnItem.reason && (
                <div className="return-reason">
                  <strong>Reason:</strong> {getReasonDisplay(returnItem.reason)}
                </div>
              )}

              {/* Notes */}
              {returnItem.notes && (
                <div className="return-notes">
                  <strong>Notes:</strong> {returnItem.notes}
                </div>
              )}

              {/* Timestamps */}
              <div className="return-timestamps">
                <small>
                  Created: {formatDate(returnItem.createdAt)}
                  {returnItem.updatedAt !== returnItem.createdAt && (
                    <> • Updated: {formatDate(returnItem.updatedAt)}</>
                  )}
                </small>
              </div>
            </div>

            {(hasPermission('canEdit') || hasPermission('canDelete')) && (
              <div className="return-card-actions">
                {hasPermission('canEdit') && (
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={() => onEditReturn(returnItem)}
                    title="Edit return"
                  >
                    ✏️ Edit
                  </button>
                )}
                {hasPermission('canDelete') && (
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete this return?')) {
                        onDeleteReturn(returnItem.id);
                      }
                    }}
                    title="Delete return"
                  >
                    🗑️ Delete
                  </button>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReturnList;
