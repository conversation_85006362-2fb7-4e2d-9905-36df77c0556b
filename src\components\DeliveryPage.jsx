import React, { useState, useEffect } from 'react';
import deliveryService from '../services/deliveryService.js';
import DeliveryForm from './DeliveryForm.jsx';
import DeliveryList from './DeliveryList.jsx';
import DeliveryDashboard from './DeliveryDashboard.jsx';
import { useAuth, PermissionWrapper } from '../hooks/useAuth.js';
import './DeliveryPage.css';

const DeliveryPage = () => {
  const [deliveries, setDeliveries] = useState([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDelivery, setEditingDelivery] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [stats, setStats] = useState(null);
  const [activeView, setActiveView] = useState('list'); // 'list' or 'dashboard'

  const { hasPermission } = useAuth();

  // Load deliveries on component mount
  useEffect(() => {
    loadDeliveries();
    loadStats();
  }, []);

  const loadDeliveries = () => {
    setLoading(true);
    try {
      const allDeliveries = deliveryService.getAllDeliveries();
      setDeliveries(allDeliveries);
    } catch (error) {
      console.error('Error loading deliveries:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = () => {
    try {
      const deliveryStats = deliveryService.getDeliveryStats();
      setStats(deliveryStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const handleCreateDelivery = () => {
    setEditingDelivery(null);
    setIsFormOpen(true);
  };

  const handleEditDelivery = (delivery) => {
    setEditingDelivery(delivery);
    setIsFormOpen(true);
  };

  const handleDeliveryCreated = (delivery) => {
    loadDeliveries();
    loadStats();
    // Trigger a refresh of the main app to update inventory display
    window.dispatchEvent(new CustomEvent('inventoryUpdated'));
  };

  const handleDeleteDelivery = async (deliveryId) => {
    try {
      await deliveryService.deleteDelivery(deliveryId);
      loadDeliveries();
      loadStats();
      // Trigger a refresh of the main app to update inventory display
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));
    } catch (error) {
      alert(`Error deleting delivery: ${error.message}`);
    }
  };

  const handleRefresh = () => {
    loadDeliveries();
    loadStats();
    // Trigger a refresh of the main app to update inventory display
    window.dispatchEvent(new CustomEvent('inventoryUpdated'));
  };

  // Calculate total dispatched quantities for each product
  const getDispatchedQuantities = () => {
    const quantities = {};

    deliveries.forEach(delivery => {
      if (delivery.status === 'dispatched' || delivery.status === 'delivered') {
        delivery.items.forEach(item => {
          if (quantities[item.productId]) {
            quantities[item.productId].quantity += item.quantity;
          } else {
            quantities[item.productId] = {
              productName: item.productName,
              quantity: item.quantity
            };
          }
        });
      }
    });

    return Object.entries(quantities)
      .map(([productId, data]) => ({
        productId,
        productName: data.productName,
        totalDispatched: data.quantity
      }))
      .sort((a, b) => b.totalDispatched - a.totalDispatched);
  };

  // Filter deliveries based on search and date
  const filteredDeliveries = deliveries.filter(delivery => {
    const matchesSearch = searchQuery === '' ||
      delivery.notes?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      delivery.items.some(item =>
        item.productName?.toLowerCase().includes(searchQuery.toLowerCase())
      );

    const matchesDate = dateFilter === '' || 
      new Date(delivery.date).toDateString() === new Date(dateFilter).toDateString();

    return matchesSearch && matchesDate;
  });

  if (loading) {
    return (
      <div className="delivery-page">
        <div className="loading-state">
          <h2>📦 Loading Deliveries...</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="delivery-page">
      {/* Header */}
      <div className="delivery-header">
        <div className="header-content">
          <h1>🚚 Delivery Management</h1>
          <p>Track and manage your product deliveries</p>
        </div>
        <div className="header-actions">
          <div className="view-toggle">
            <button
              className={`toggle-btn ${activeView === 'list' ? 'active' : ''}`}
              onClick={() => setActiveView('list')}
            >
              📋 List
            </button>
            <button
              className={`toggle-btn ${activeView === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveView('dashboard')}
            >
              📊 Dashboard
            </button>
          </div>
          <PermissionWrapper permission="canCreate">
            <button
              className="btn btn-primary create-delivery-btn"
              onClick={handleCreateDelivery}
            >
              📦 New Delivery
            </button>
          </PermissionWrapper>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="delivery-stats">
          <div className="stat-card">
            <div className="stat-value">{stats.totalDeliveries}</div>
            <div className="stat-label">Total Deliveries</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.totalItemsDispatched}</div>
            <div className="stat-label">Items Dispatched</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.deliveriesByStatus.dispatched}</div>
            <div className="stat-label">📤 Dispatched</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.deliveriesByStatus.delivered}</div>
            <div className="stat-label">✅ Delivered</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{stats.deliveriesByStatus.returned}</div>
            <div className="stat-label">↩️ Returned</div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="delivery-filters">
        <div className="search-group">
          <input
            type="text"
            placeholder="🔍 Search deliveries..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="date-filter-group">
          <label htmlFor="dateFilter">Filter by Date:</label>
          <input
            type="date"
            id="dateFilter"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="date-filter"
          />
          {dateFilter && (
            <button
              className="clear-filter-btn"
              onClick={() => setDateFilter('')}
              title="Clear date filter"
            >
              ✕
            </button>
          )}
        </div>

        <div className="results-count">
          Showing {filteredDeliveries.length} of {deliveries.length} deliveries
        </div>
      </div>

      {/* Dispatched Quantities Summary */}
      {searchQuery === '' && dateFilter === '' && (
        <div className="dispatched-summary">
          <h3>📦 Total Dispatched Quantities</h3>
          <div className="dispatched-grid">
            {getDispatchedQuantities().slice(0, 10).map(item => (
              <div key={item.productId} className="dispatched-item">
                <div className="dispatched-name">{item.productName}</div>
                <div className="dispatched-quantity">{item.totalDispatched}</div>
              </div>
            ))}
            {getDispatchedQuantities().length === 0 && (
              <div className="no-data">No dispatched items yet</div>
            )}
          </div>
          {getDispatchedQuantities().length > 10 && (
            <div className="show-more">
              <small>Showing top 10 items. Total: {getDispatchedQuantities().length} products</small>
            </div>
          )}
        </div>
      )}

      {/* Recent Deliveries Quick View */}
      {stats && stats.recentDeliveries.length > 0 && searchQuery === '' && dateFilter === '' && (
        <div className="recent-deliveries">
          <h3>📋 Recent Deliveries</h3>
          <div className="recent-list">
            {stats.recentDeliveries.map(delivery => (
              <div key={delivery.id} className="recent-item">
                <div className="recent-info">
                  <strong>#{delivery.id}</strong>
                  <span>{new Date(delivery.date).toLocaleDateString()}</span>
                  <span>{delivery.totalItems} items</span>
                </div>
                <div className="recent-status">
                  {delivery.status === 'dispatched' && '📤'}
                  {delivery.status === 'delivered' && '✅'}
                  {delivery.status === 'returned' && '↩️'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Content */}
      {activeView === 'dashboard' ? (
        <DeliveryDashboard />
      ) : (
        <DeliveryList
          deliveries={filteredDeliveries}
          onEditDelivery={handleEditDelivery}
          onDeleteDelivery={handleDeleteDelivery}
          onRefresh={handleRefresh}
        />
      )}

      {/* Delivery Form Modal */}
      <PermissionWrapper permission="canCreate">
        <DeliveryForm
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onDeliveryCreated={handleDeliveryCreated}
          editingDelivery={editingDelivery}
        />
      </PermissionWrapper>

      {/* Quick Actions */}
      <div className="quick-actions">
        <button
          className="quick-action-btn"
          onClick={handleRefresh}
          title="Refresh deliveries"
        >
          🔄
        </button>
        <button
          className="quick-action-btn"
          onClick={() => {
            setSearchQuery('');
            setDateFilter('');
          }}
          title="Clear all filters"
        >
          🧹
        </button>
      </div>
    </div>
  );
};

export default DeliveryPage;
