/* Delivery List Styles */
.delivery-list {
  padding: 1rem;
}

.delivery-list-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-state {
  text-align: center;
  color: #666;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

/* Controls */
.delivery-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  align-items: center;
  flex-wrap: wrap;
}

.filter-group,
.sort-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label,
.sort-group label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.filter-group select,
.sort-group select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.sort-order-btn {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.sort-order-btn:hover {
  background: #f5f5f5;
}

/* Delivery Grid */
.delivery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

/* Delivery Card */
.delivery-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.delivery-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.delivery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.delivery-id {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.delivery-date {
  font-size: 0.8rem;
  opacity: 0.9;
}

.delivery-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Customer Info */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.customer-info .phone {
  font-size: 0.9rem;
  color: #666;
}

/* Items Summary */
.items-summary {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.item-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-name {
  font-weight: 500;
  color: #333;
}

.item-quantity {
  color: #667eea;
  font-weight: 500;
}

.more-items {
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
  text-align: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
}

/* Notes */
.delivery-notes {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.delivery-notes p {
  margin: 0;
  color: #666;
  font-style: italic;
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  border-left: 3px solid #667eea;
}

/* Actions */
.delivery-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.status-actions,
.crud-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

/* Status Badges */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

.status-dispatched {
  background: rgba(255, 243, 205, 0.9);
  color: #856404;
  border: 1px solid rgba(255, 234, 167, 0.9);
}

.status-delivered {
  background: rgba(212, 237, 218, 0.9);
  color: #155724;
  border: 1px solid rgba(195, 230, 203, 0.9);
}

.status-returned {
  background: rgba(248, 215, 218, 0.9);
  color: #721c24;
  border: 1px solid rgba(245, 198, 203, 0.9);
}

/* Delivery Details */
.delivery-details {
  padding: 1rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.delivery-details h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.detailed-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.detailed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.address-info {
  margin: 1rem 0;
}

.address-info p {
  margin: 0.25rem 0 0 0;
  color: #666;
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.timestamps {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

.timestamps small {
  color: #666;
  font-size: 0.8rem;
}

/* Toggle Details Button */
.toggle-details-btn {
  width: 100%;
  padding: 0.75rem;
  background: #667eea;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.toggle-details-btn:hover {
  background: #5a6fd8;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .delivery-grid {
    grid-template-columns: 1fr;
  }

  .delivery-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .filter-group,
  .sort-group {
    justify-content: space-between;
  }

  .delivery-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .status-actions,
  .crud-actions {
    justify-content: center;
  }
}
