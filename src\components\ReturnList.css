/* Return List Styles */
.return-list {
  padding: 1rem;
}

.return-list-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-state {
  text-align: center;
  color: #666;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

/* Controls */
.return-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
}

.sort-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-group label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.sort-group select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
}

.sort-order-btn {
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.sort-order-btn:hover {
  background: #e9ecef;
}

.results-info {
  color: #666;
  font-size: 0.9rem;
}

/* Return Cards */
.return-cards {
  display: grid;
  gap: 1.5rem;
}

.return-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.return-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.return-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e0e0e0;
}

.return-info h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.2rem;
}

.return-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.return-date,
.return-id {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.return-status {
  flex-shrink: 0;
}

.return-card-body {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Items */
.return-items h4 {
  margin: 0 0 0.75rem 0;
  color: #333;
  font-size: 1rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.item-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.item-entry:last-child {
  border-bottom: none;
}

.item-name {
  font-weight: 500;
  color: #333;
}

.item-quantity {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Reason and Notes */
.return-reason,
.return-notes {
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
  font-size: 0.9rem;
  line-height: 1.4;
}

.return-reason strong,
.return-notes strong {
  color: #333;
}

/* Timestamps */
.return-timestamps {
  padding-top: 0.5rem;
  border-top: 1px solid #e9ecef;
  color: #666;
  font-size: 0.8rem;
}

/* Actions */
.return-card-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

/* Status Badges */
.status-badge {
  padding: 0.4rem 0.8rem;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.status-received {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-processed {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .return-list {
    padding: 0.5rem;
  }

  .return-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .sort-group {
    justify-content: space-between;
  }

  .return-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .return-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .return-card-actions {
    flex-direction: column;
  }

  .items-list {
    padding: 0.75rem;
  }

  .item-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Animation */
.return-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State */
.return-list.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Hover Effects */
.return-card-actions .btn {
  transform: translateY(0);
  transition: all 0.2s ease;
}

.return-card-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
