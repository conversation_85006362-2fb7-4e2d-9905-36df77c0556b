import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth.js';
import './LoginForm.css';

const LoginForm = ({ onLoginSuccess }) => {
  const { login, loading, error, setError } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.username.trim() || !formData.password.trim()) {
      setError('Please enter both username and password');
      return;
    }

    try {
      const user = await login(formData.username.trim(), formData.password);
      console.log('Login successful:', user);
      if (onLoginSuccess) {
        onLoginSuccess(user);
      }
    } catch (err) {
      console.error('Login failed:', err);
      // Error is already set by the login function
    }
  };

  const handleDemoLogin = (role) => {
    if (role === 'admin') {
      setFormData({ username: 'admin', password: 'admin123' });
    } else {
      setFormData({ username: 'viewer', password: 'viewer123' });
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1>🏪 Inventory Management</h1>
          <p>Please sign in to access the system</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          {error && (
            <div className="error-message">
              <span className="error-icon">⚠️</span>
              {error}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="Enter your username"
              required
              disabled={loading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="password-input-container">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
              >
                {showPassword ? '👁️' : '👁️‍🗨️'}
              </button>
            </div>
          </div>

          <button
            type="submit"
            className="login-button"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="loading-spinner">⏳</span>
                Signing in...
              </>
            ) : (
              <>
                <span className="login-icon">🔐</span>
                Sign In
              </>
            )}
          </button>
        </form>

        <div className="demo-accounts">
          <h3>Demo Accounts</h3>
          <div className="demo-buttons">
            <button
              type="button"
              className="demo-button admin-demo"
              onClick={() => handleDemoLogin('admin')}
              disabled={loading}
            >
              <span className="demo-icon">👑</span>
              <div className="demo-info">
                <strong>Admin Access</strong>
                <small>Full permissions</small>
              </div>
            </button>
            
            <button
              type="button"
              className="demo-button viewer-demo"
              onClick={() => handleDemoLogin('viewer')}
              disabled={loading}
            >
              <span className="demo-icon">👀</span>
              <div className="demo-info">
                <strong>Viewer Access</strong>
                <small>Read-only permissions</small>
              </div>
            </button>
          </div>
        </div>

        <div className="login-footer">
          <p>
            <strong>Admin:</strong> username: admin, password: admin123<br/>
            <strong>Viewer:</strong> username: viewer, password: viewer123
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
