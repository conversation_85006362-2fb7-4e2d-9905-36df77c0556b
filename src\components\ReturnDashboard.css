/* Return Dashboard Styles */
.return-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Loading and Error States */
.return-dashboard.loading,
.return-dashboard.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  font-size: 1.2rem;
  color: #666;
}

.error-message {
  color: #e74c3c;
  font-size: 1.2rem;
}

/* Overview Cards */
.dashboard-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.overview-card.total::before {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.overview-card.items::before {
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.overview-card.received::before {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.overview-card.pending::before {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.card-icon {
  font-size: 2.5rem;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 50%;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.card-label {
  color: #666;
  font-size: 1rem;
  margin-top: 0.25rem;
}

.card-percentage {
  color: #e74c3c;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.25rem;
}

/* Charts Section */
.dashboard-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.chart-card h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Status Chart */
.status-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-bar {
  height: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
}

.status-segment {
  height: 100%;
  transition: all 0.3s ease;
}

.status-segment.received {
  background: linear-gradient(90deg, #27ae60, #229954);
}

.status-segment.pending {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.status-segment.processed {
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.status-legend {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.received {
  background: #27ae60;
}

.legend-color.pending {
  background: #f39c12;
}

.legend-color.processed {
  background: #3498db;
}

/* Reasons Chart */
.reasons-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.reason-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.reason-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reason-name {
  font-weight: 500;
  color: #333;
}

.reason-count {
  background: #e74c3c;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.reason-bar {
  height: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
}

.reason-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.no-data {
  text-align: center;
  color: #666;
  padding: 2rem;
  font-style: italic;
}

/* Activity Section */
.dashboard-activity {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

.activity-card,
.quick-stats-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.activity-card h3,
.quick-stats-card h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
}

.activity-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.activity-details {
  font-size: 0.9rem;
  color: #666;
}

.activity-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.status-received {
  background: #d4edda;
  color: #155724;
}

.status-badge.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.status-processed {
  background: #d1ecf1;
  color: #0c5460;
}

.no-activity {
  text-align: center;
  color: #666;
  padding: 2rem;
  font-style: italic;
}

/* Quick Stats */
.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quick-stat {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.quick-stat .stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
}

/* Dashboard Actions */
.dashboard-actions {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .return-dashboard {
    padding: 1rem;
    gap: 1.5rem;
  }

  .dashboard-overview {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .overview-card {
    padding: 1rem;
  }

  .card-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .card-number {
    font-size: 2rem;
  }

  .dashboard-charts {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .dashboard-activity {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .status-legend {
    flex-direction: column;
    gap: 0.5rem;
  }

  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .activity-status {
    align-self: flex-end;
  }
}

/* Animation */
.return-dashboard {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.overview-card,
.chart-card,
.activity-card,
.quick-stats-card {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
