import React, { useState } from 'react';
import deliveryService from '../services/deliveryService.js';
import './DeliveryList.css';

const DeliveryList = ({ deliveries, onEditDelivery, onDeleteDelivery, onRefresh }) => {
  const [selectedDelivery, setSelectedDelivery] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');

  // Filter and sort deliveries
  const filteredDeliveries = deliveries
    .filter(delivery => filterStatus === 'all' || delivery.status === filterStatus)
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date);
          bValue = new Date(b.date);
          break;
        case 'customer':
          aValue = a.id || '';
          bValue = b.id || '';
          break;
        case 'items':
          aValue = a.totalItems;
          bValue = b.totalItems;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const handleStatusChange = async (deliveryId, newStatus) => {
    try {
      await deliveryService.updateDelivery(deliveryId, { status: newStatus });
      onRefresh();
    } catch (error) {
      alert(`Error updating status: ${error.message}`);
    }
  };

  const handleDeleteClick = (delivery) => {
    if (window.confirm(`Are you sure you want to delete delivery #${delivery.id}?`)) {
      onDeleteDelivery(delivery.id);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      dispatched: { emoji: '📤', label: 'Dispatched', class: 'status-dispatched' },
      delivered: { emoji: '✅', label: 'Delivered', class: 'status-delivered' },
      returned: { emoji: '↩️', label: 'Returned', class: 'status-returned' }
    };
    
    const config = statusConfig[status] || statusConfig.dispatched;
    return (
      <span className={`status-badge ${config.class}`}>
        {config.emoji} {config.label}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (deliveries.length === 0) {
    return (
      <div className="delivery-list-empty">
        <div className="empty-state">
          <h3>📦 No Deliveries Yet</h3>
          <p>Create your first delivery to start tracking dispatches.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="delivery-list">
      {/* Filters and Sorting */}
      <div className="delivery-controls">
        <div className="filter-group">
          <label htmlFor="statusFilter">Filter by Status:</label>
          <select
            id="statusFilter"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="dispatched">📤 Dispatched</option>
            <option value="delivered">✅ Delivered</option>
            <option value="returned">↩️ Returned</option>
          </select>
        </div>

        <div className="sort-group">
          <label htmlFor="sortBy">Sort by:</label>
          <select
            id="sortBy"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
          >
            <option value="date">Date</option>
            <option value="items">Items Count</option>
            <option value="status">Status</option>
          </select>
          
          <button
            className="sort-order-btn"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      {/* Delivery Cards */}
      <div className="delivery-grid">
        {filteredDeliveries.map(delivery => (
          <div key={delivery.id} className="delivery-card">
            <div className="delivery-header">
              <div className="delivery-id">
                <strong>Delivery #{delivery.id}</strong>
                <span className="delivery-date">{formatDate(delivery.date)}</span>
              </div>
              {getStatusBadge(delivery.status)}
            </div>

            <div className="delivery-content">
              {/* Items Summary */}
              <div className="items-summary">
                <strong>📋 Items ({delivery.totalItems} total):</strong>
                <div className="items-list">
                  {delivery.items.slice(0, 3).map((item, index) => (
                    <div key={index} className="item-summary">
                      <span className="item-name">{item.productName}</span>
                      <span className="item-quantity">×{item.quantity}</span>
                    </div>
                  ))}
                  {delivery.items.length > 3 && (
                    <div className="more-items">
                      +{delivery.items.length - 3} more items
                    </div>
                  )}
                </div>
              </div>

              {/* Notes */}
              {delivery.notes && (
                <div className="delivery-notes">
                  <strong>📝 Notes:</strong>
                  <p>{delivery.notes}</p>
                </div>
              )}
            </div>

            <div className="delivery-actions">
              {/* Status Change Buttons */}
              <div className="status-actions">
                {delivery.status !== 'delivered' && (
                  <button
                    className="btn btn-success btn-sm"
                    onClick={() => handleStatusChange(delivery.id, 'delivered')}
                    title="Mark as Delivered"
                  >
                    ✅
                  </button>
                )}
                {delivery.status !== 'returned' && (
                  <button
                    className="btn btn-warning btn-sm"
                    onClick={() => handleStatusChange(delivery.id, 'returned')}
                    title="Mark as Returned"
                  >
                    ↩️
                  </button>
                )}
                {delivery.status !== 'dispatched' && (
                  <button
                    className="btn btn-info btn-sm"
                    onClick={() => handleStatusChange(delivery.id, 'dispatched')}
                    title="Mark as Dispatched"
                  >
                    📤
                  </button>
                )}
              </div>

              {/* Edit and Delete */}
              <div className="crud-actions">
                <button
                  className="btn btn-secondary btn-sm"
                  onClick={() => onEditDelivery(delivery)}
                  title="Edit Delivery"
                >
                  ✏️
                </button>
                <button
                  className="btn btn-danger btn-sm"
                  onClick={() => handleDeleteClick(delivery)}
                  title="Delete Delivery"
                >
                  🗑️
                </button>
              </div>
            </div>

            {/* Expanded Details */}
            {selectedDelivery === delivery.id && (
              <div className="delivery-details">
                <h4>📋 All Items:</h4>
                <div className="detailed-items">
                  {delivery.items.map((item, index) => (
                    <div key={index} className="detailed-item">
                      <span className="item-name">{item.productName}</span>
                      <span className="item-quantity">Quantity: {item.quantity}</span>
                    </div>
                  ))}
                </div>

                <div className="timestamps">
                  <small>Created: {new Date(delivery.createdAt).toLocaleString()}</small>
                  {delivery.updatedAt !== delivery.createdAt && (
                    <small>Updated: {new Date(delivery.updatedAt).toLocaleString()}</small>
                  )}
                </div>
              </div>
            )}

            {/* Toggle Details Button */}
            <button
              className="toggle-details-btn"
              onClick={() => setSelectedDelivery(
                selectedDelivery === delivery.id ? null : delivery.id
              )}
            >
              {selectedDelivery === delivery.id ? 'Hide Details ▲' : 'Show Details ▼'}
            </button>
          </div>
        ))}
      </div>

      {filteredDeliveries.length === 0 && (
        <div className="no-results">
          <p>No deliveries found matching the current filters.</p>
        </div>
      )}
    </div>
  );
};

export default DeliveryList;
