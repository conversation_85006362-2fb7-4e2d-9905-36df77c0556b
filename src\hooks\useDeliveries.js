import { useState, useEffect, useCallback } from 'react';
import deliveryService from '../services/deliveryService.js';

export const useDeliveries = () => {
  const [deliveries, setDeliveries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load all deliveries
  const loadDeliveries = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const allDeliveries = deliveryService.getAllDeliveries();
      setDeliveries(allDeliveries);
    } catch (err) {
      setError(err.message);
      console.error('Error loading deliveries:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new delivery
  const createDelivery = useCallback(async (deliveryData) => {
    try {
      setError(null);
      const newDelivery = deliveryService.createDelivery(deliveryData);
      setDeliveries(prev => [newDelivery, ...prev]);
      return newDelivery;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  // Update delivery
  const updateDelivery = useCallback(async (deliveryId, updates) => {
    try {
      setError(null);
      const updatedDelivery = deliveryService.updateDelivery(deliveryId, updates);
      setDeliveries(prev => 
        prev.map(delivery => 
          delivery.id === deliveryId ? updatedDelivery : delivery
        )
      );
      return updatedDelivery;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  // Delete delivery
  const deleteDelivery = useCallback(async (deliveryId) => {
    try {
      setError(null);
      await deliveryService.deleteDelivery(deliveryId);
      setDeliveries(prev => prev.filter(delivery => delivery.id !== deliveryId));
      return true;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, []);

  // Get delivery by ID
  const getDelivery = useCallback((deliveryId) => {
    return deliveries.find(delivery => delivery.id === deliveryId);
  }, [deliveries]);

  // Search deliveries
  const searchDeliveries = useCallback((query) => {
    try {
      return deliveryService.searchDeliveries(query);
    } catch (err) {
      setError(err.message);
      return [];
    }
  }, []);

  // Get deliveries by date range
  const getDeliveriesByDateRange = useCallback((startDate, endDate) => {
    try {
      return deliveryService.getDeliveriesByDateRange(startDate, endDate);
    } catch (err) {
      setError(err.message);
      return [];
    }
  }, []);

  // Get deliveries by date
  const getDeliveriesByDate = useCallback((date) => {
    try {
      return deliveryService.getDeliveriesByDate(date);
    } catch (err) {
      setError(err.message);
      return [];
    }
  }, []);

  // Get delivery statistics
  const getDeliveryStats = useCallback(() => {
    try {
      return deliveryService.getDeliveryStats();
    } catch (err) {
      setError(err.message);
      return {
        totalDeliveries: 0,
        totalItemsDispatched: 0,
        deliveriesByStatus: { dispatched: 0, delivered: 0, returned: 0 },
        deliveriesByDate: {},
        recentDeliveries: []
      };
    }
  }, []);

  // Refresh deliveries
  const refreshDeliveries = useCallback(() => {
    loadDeliveries();
  }, [loadDeliveries]);

  // Load deliveries on mount
  useEffect(() => {
    loadDeliveries();
  }, [loadDeliveries]);

  return {
    deliveries,
    loading,
    error,
    createDelivery,
    updateDelivery,
    deleteDelivery,
    getDelivery,
    searchDeliveries,
    getDeliveriesByDateRange,
    getDeliveriesByDate,
    getDeliveryStats,
    refreshDeliveries
  };
};
