import React from 'react';
import { useAuth } from '../hooks/useAuth.js';
import './PurchaseList.css';

const PurchaseList = ({ purchases, onEditPurchase, onDeletePurchase, onRefresh }) => {
  const { hasPermission } = useAuth();
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return `৳${amount.toFixed(2)}`;
  };

  const handleDelete = (purchase) => {
    if (window.confirm(`Are you sure you want to delete the purchase from ${formatDate(purchase.date)}? This will remove ${purchase.totalQuantity} items worth ${formatCurrency(purchase.totalAmount)} from your inventory.`)) {
      onDeletePurchase(purchase.id);
    }
  };

  if (purchases.length === 0) {
    return (
      <div className="empty-state">
        <div className="empty-icon">📦</div>
        <h3>No Purchases Found</h3>
        <p>Start by creating your first purchase to track your inventory.</p>
      </div>
    );
  }

  return (
    <div className="purchase-list">
      <div className="list-header">
        <h3>Purchase History ({purchases.length})</h3>
        <button className="btn btn-secondary" onClick={onRefresh}>
          🔄 Refresh
        </button>
      </div>

      <div className="purchase-cards">
        {purchases.map(purchase => (
          <div key={purchase.id} className="purchase-card">
            <div className="purchase-header">
              <div className="purchase-date">
                <span className="date-label">📅</span>
                <span className="date-value">{formatDate(purchase.date)}</span>
              </div>
              {(hasPermission('canEdit') || hasPermission('canDelete')) && (
                <div className="purchase-actions">
                  {hasPermission('canEdit') && (
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={() => onEditPurchase(purchase)}
                      title="Edit Purchase"
                    >
                      ✏️ Edit
                    </button>
                  )}
                  {hasPermission('canDelete') && (
                    <button
                      className="btn btn-sm btn-danger"
                      onClick={() => handleDelete(purchase)}
                      title="Delete Purchase"
                    >
                      🗑️ Delete
                    </button>
                  )}
                </div>
              )}
            </div>

            <div className="purchase-summary">
              <div className="summary-item">
                <span className="summary-label">Items:</span>
                <span className="summary-value">{purchase.totalQuantity}</span>
              </div>
              <div className="summary-item total">
                <span className="summary-label">Total:</span>
                <span className="summary-value">{formatCurrency(purchase.totalAmount)}</span>
              </div>
            </div>

            <div className="purchase-items">
              <h4>Items Purchased:</h4>
              <div className="items-grid">
                {purchase.items.map((item, index) => (
                  <div key={index} className="item-row">
                    <div className="item-name">{item.itemName}</div>
                    <div className="item-details">
                      <span className="item-quantity">{item.quantity}x</span>
                      <span className="item-price">{formatCurrency(item.unitPrice)}</span>
                      <span className="item-total">{formatCurrency(item.totalPrice)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {purchase.notes && (
              <div className="purchase-notes">
                <h4>Notes:</h4>
                <p>{purchase.notes}</p>
              </div>
            )}

            <div className="purchase-meta">
              <span className="meta-item">
                Created: {new Date(purchase.createdAt).toLocaleString()}
              </span>
              {purchase.updatedAt !== purchase.createdAt && (
                <span className="meta-item">
                  Updated: {new Date(purchase.updatedAt).toLocaleString()}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PurchaseList;
