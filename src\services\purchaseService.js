import databaseService from './databaseService.js';

const PURCHASE_STORAGE_KEY = 'inventory_purchases';

class PurchaseService {
  constructor() {
    this.initializeStorage();
  }

  initializeStorage() {
    const existingData = localStorage.getItem(PURCHASE_STORAGE_KEY);
    if (!existingData) {
      const initialData = {
        purchases: [],
        metadata: {
          version: '1.0.0',
          createdAt: new Date().toISOString(),
          lastUpdated: new Date().toISOString()
        }
      };
      localStorage.setItem(PURCHASE_STORAGE_KEY, JSON.stringify(initialData));
    }
  }

  // Generate unique ID for purchases
  generateId() {
    return 'purchase_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Get all purchases
  getAllPurchases() {
    try {
      const data = JSON.parse(localStorage.getItem(PURCHASE_STORAGE_KEY));
      return data.purchases || [];
    } catch (error) {
      console.error('Error loading purchases:', error);
      return [];
    }
  }

  // Get purchase by ID
  getPurchaseById(id) {
    const purchases = this.getAllPurchases();
    return purchases.find(purchase => purchase.id === id);
  }

  // Create new purchase
  async createPurchase(purchaseData) {
    try {
      const data = JSON.parse(localStorage.getItem(PURCHASE_STORAGE_KEY));
      
      // Calculate totals for each item and overall purchase
      const processedItems = purchaseData.items.map(item => ({
        ...item,
        totalPrice: parseFloat(item.unitPrice) * parseInt(item.quantity),
        productId: item.productId || this.generateProductId(item.itemName)
      }));

      const totalAmount = processedItems.reduce((sum, item) => sum + item.totalPrice, 0);
      const totalQuantity = processedItems.reduce((sum, item) => sum + parseInt(item.quantity), 0);

      const newPurchase = {
        id: this.generateId(),
        date: purchaseData.date,
        items: processedItems,
        totalAmount: totalAmount,
        totalQuantity: totalQuantity,
        notes: purchaseData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      data.purchases.push(newPurchase);
      data.metadata.lastUpdated = new Date().toISOString();
      
      localStorage.setItem(PURCHASE_STORAGE_KEY, JSON.stringify(data));

      // Update inventory with purchased items
      await this.updateInventoryFromPurchase(newPurchase, 'add');

      // Dispatch custom event for inventory updates
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));
      window.dispatchEvent(new CustomEvent('purchaseCreated', { detail: newPurchase }));

      return newPurchase;
    } catch (error) {
      console.error('Error creating purchase:', error);
      throw new Error('Failed to create purchase: ' + error.message);
    }
  }

  // Update existing purchase
  async updatePurchase(id, updates) {
    try {
      const data = JSON.parse(localStorage.getItem(PURCHASE_STORAGE_KEY));
      const purchaseIndex = data.purchases.findIndex(p => p.id === id);
      
      if (purchaseIndex === -1) {
        throw new Error('Purchase not found');
      }

      const oldPurchase = { ...data.purchases[purchaseIndex] };
      
      // Process updated items
      const processedItems = updates.items.map(item => ({
        ...item,
        totalPrice: parseFloat(item.unitPrice) * parseInt(item.quantity),
        productId: item.productId || this.generateProductId(item.itemName)
      }));

      const totalAmount = processedItems.reduce((sum, item) => sum + item.totalPrice, 0);
      const totalQuantity = processedItems.reduce((sum, item) => sum + parseInt(item.quantity), 0);

      const updatedPurchase = {
        ...oldPurchase,
        ...updates,
        items: processedItems,
        totalAmount: totalAmount,
        totalQuantity: totalQuantity,
        updatedAt: new Date().toISOString()
      };

      // Revert old purchase from inventory
      await this.updateInventoryFromPurchase(oldPurchase, 'remove');
      
      // Apply new purchase to inventory
      await this.updateInventoryFromPurchase(updatedPurchase, 'add');

      data.purchases[purchaseIndex] = updatedPurchase;
      data.metadata.lastUpdated = new Date().toISOString();
      
      localStorage.setItem(PURCHASE_STORAGE_KEY, JSON.stringify(data));

      // Dispatch custom events
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));
      window.dispatchEvent(new CustomEvent('purchaseUpdated', { detail: updatedPurchase }));

      return updatedPurchase;
    } catch (error) {
      console.error('Error updating purchase:', error);
      throw new Error('Failed to update purchase: ' + error.message);
    }
  }

  // Delete purchase
  async deletePurchase(id) {
    try {
      const data = JSON.parse(localStorage.getItem(PURCHASE_STORAGE_KEY));
      const purchaseIndex = data.purchases.findIndex(p => p.id === id);
      
      if (purchaseIndex === -1) {
        throw new Error('Purchase not found');
      }

      const purchaseToDelete = data.purchases[purchaseIndex];
      
      // Remove purchase from inventory
      await this.updateInventoryFromPurchase(purchaseToDelete, 'remove');

      data.purchases.splice(purchaseIndex, 1);
      data.metadata.lastUpdated = new Date().toISOString();
      
      localStorage.setItem(PURCHASE_STORAGE_KEY, JSON.stringify(data));

      // Dispatch custom events
      window.dispatchEvent(new CustomEvent('inventoryUpdated'));
      window.dispatchEvent(new CustomEvent('purchaseDeleted', { detail: { id } }));

      return true;
    } catch (error) {
      console.error('Error deleting purchase:', error);
      throw new Error('Failed to delete purchase: ' + error.message);
    }
  }

  // Generate product ID from item name
  generateProductId(itemName) {
    return itemName.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }

  // Update inventory from purchase
  async updateInventoryFromPurchase(purchase, operation) {
    try {
      for (const item of purchase.items) {
        const existingProduct = databaseService.getProductById(item.productId);
        
        if (existingProduct) {
          // Update existing product
          const quantityChange = operation === 'add' ? item.quantity : -item.quantity;
          const newQuantity = Math.max(0, existingProduct.quantity + quantityChange);
          
          // Update purchase price and cost for inventory valuation
          const updateData = {
            quantity: newQuantity
          };

          // Update purchase price if this is a new purchase
          if (operation === 'add') {
            updateData.purchasePrice = item.unitPrice;
            updateData.lastPurchaseDate = purchase.date;
          }

          await databaseService.updateProduct(item.productId, updateData);
        } else if (operation === 'add') {
          // Create new product from purchase
          const newProduct = {
            id: item.productId,
            name: item.itemName,
            category: 'Purchased Items',
            quantity: item.quantity,
            purchasePrice: item.unitPrice,
            price: item.unitPrice * 1.5, // Default markup
            description: `Auto-created from purchase on ${purchase.date}`,
            lastPurchaseDate: purchase.date,
            createdAt: new Date().toISOString()
          };

          await databaseService.addProduct(newProduct);
        }
      }
    } catch (error) {
      console.error('Error updating inventory from purchase:', error);
      throw error;
    }
  }

  // Get purchase statistics
  getPurchaseStats() {
    const purchases = this.getAllPurchases();
    
    const stats = {
      totalPurchases: purchases.length,
      totalAmount: 0,
      totalQuantity: 0,
      averageOrderValue: 0,
      purchasesByMonth: {},
      topItems: {},
      recentPurchases: []
    };

    purchases.forEach(purchase => {
      stats.totalAmount += purchase.totalAmount;
      stats.totalQuantity += purchase.totalQuantity;
      
      // Group by month
      const monthKey = purchase.date.substring(0, 7); // YYYY-MM
      if (!stats.purchasesByMonth[monthKey]) {
        stats.purchasesByMonth[monthKey] = { count: 0, amount: 0 };
      }
      stats.purchasesByMonth[monthKey].count++;
      stats.purchasesByMonth[monthKey].amount += purchase.totalAmount;

      // Track top items
      purchase.items.forEach(item => {
        if (!stats.topItems[item.itemName]) {
          stats.topItems[item.itemName] = { quantity: 0, amount: 0 };
        }
        stats.topItems[item.itemName].quantity += item.quantity;
        stats.topItems[item.itemName].amount += item.totalPrice;
      });
    });

    stats.averageOrderValue = stats.totalPurchases > 0 ? stats.totalAmount / stats.totalPurchases : 0;
    stats.recentPurchases = purchases
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    return stats;
  }

  // Search purchases
  searchPurchases(query) {
    const purchases = this.getAllPurchases();
    const searchTerm = query.toLowerCase();
    
    return purchases.filter(purchase => {
      return (
        purchase.date.includes(searchTerm) ||
        purchase.notes?.toLowerCase().includes(searchTerm) ||
        purchase.items.some(item => 
          item.itemName?.toLowerCase().includes(searchTerm)
        )
      );
    });
  }

  // Filter purchases by date range
  filterPurchasesByDateRange(startDate, endDate) {
    const purchases = this.getAllPurchases();
    return purchases.filter(purchase => {
      const purchaseDate = new Date(purchase.date);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return purchaseDate >= start && purchaseDate <= end;
    });
  }

  // Get inventory value based on purchases
  getInventoryValue() {
    const products = databaseService.getAllProducts();
    let totalValue = 0;
    
    products.forEach(product => {
      if (product.purchasePrice && product.quantity) {
        totalValue += product.purchasePrice * product.quantity;
      }
    });
    
    return totalValue;
  }
}

const purchaseService = new PurchaseService();
export default purchaseService;
