import React, { useState, useEffect } from 'react';
import deliveryService from '../services/deliveryService.js';
import './DeliveryDashboard.css';

const DeliveryDashboard = () => {
  const [stats, setStats] = useState(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0] // today
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, [dateRange]);

  const loadStats = () => {
    setLoading(true);
    try {
      const deliveryStats = deliveryService.getDeliveryStats();
      const rangeDeliveries = deliveryService.getDeliveriesByDateRange(dateRange.start, dateRange.end);
      
      // Calculate range-specific stats
      const rangeStats = {
        ...deliveryStats,
        rangeDeliveries: rangeDeliveries.length,
        rangeItemsDispatched: rangeDeliveries.reduce((sum, d) => sum + d.totalItems, 0),
        rangeByStatus: {
          dispatched: rangeDeliveries.filter(d => d.status === 'dispatched').length,
          delivered: rangeDeliveries.filter(d => d.status === 'delivered').length,
          returned: rangeDeliveries.filter(d => d.status === 'returned').length
        }
      };

      setStats(rangeStats);
    } catch (error) {
      console.error('Error loading delivery stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDeliveryTrend = () => {
    if (!stats || !stats.deliveriesByDate) return [];
    
    const dates = Object.keys(stats.deliveriesByDate).sort();
    return dates.slice(-7).map(date => ({
      date: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      count: stats.deliveriesByDate[date]
    }));
  };

  const getStatusPercentage = (status) => {
    if (!stats || stats.totalDeliveries === 0) return 0;
    return Math.round((stats.deliveriesByStatus[status] / stats.totalDeliveries) * 100);
  };

  if (loading) {
    return (
      <div className="delivery-dashboard">
        <div className="loading">📊 Loading delivery analytics...</div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="delivery-dashboard">
        <div className="error">Failed to load delivery statistics</div>
      </div>
    );
  }

  const trend = getDeliveryTrend();

  return (
    <div className="delivery-dashboard">
      <div className="dashboard-header">
        <h2>📊 Delivery Analytics</h2>
        <div className="date-range-selector">
          <label>Date Range:</label>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
          />
          <span>to</span>
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
          />
        </div>
      </div>

      {/* Overview Stats */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalDeliveries}</div>
            <div className="stat-label">Total Deliveries</div>
            <div className="stat-sublabel">All time</div>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">📤</div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalItemsDispatched}</div>
            <div className="stat-label">Items Dispatched</div>
            <div className="stat-sublabel">All time</div>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-icon">📅</div>
          <div className="stat-content">
            <div className="stat-value">{stats.rangeDeliveries}</div>
            <div className="stat-label">Range Deliveries</div>
            <div className="stat-sublabel">Selected period</div>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <div className="stat-value">{stats.rangeItemsDispatched}</div>
            <div className="stat-label">Range Items</div>
            <div className="stat-sublabel">Selected period</div>
          </div>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className="status-section">
        <h3>📈 Delivery Status Breakdown</h3>
        <div className="status-cards">
          <div className="status-card dispatched">
            <div className="status-header">
              <span className="status-icon">📤</span>
              <span className="status-name">Dispatched</span>
            </div>
            <div className="status-stats">
              <div className="status-count">{stats.deliveriesByStatus.dispatched}</div>
              <div className="status-percentage">{getStatusPercentage('dispatched')}%</div>
            </div>
            <div className="status-bar">
              <div 
                className="status-fill dispatched-fill"
                style={{ width: `${getStatusPercentage('dispatched')}%` }}
              ></div>
            </div>
          </div>

          <div className="status-card delivered">
            <div className="status-header">
              <span className="status-icon">✅</span>
              <span className="status-name">Delivered</span>
            </div>
            <div className="status-stats">
              <div className="status-count">{stats.deliveriesByStatus.delivered}</div>
              <div className="status-percentage">{getStatusPercentage('delivered')}%</div>
            </div>
            <div className="status-bar">
              <div 
                className="status-fill delivered-fill"
                style={{ width: `${getStatusPercentage('delivered')}%` }}
              ></div>
            </div>
          </div>

          <div className="status-card returned">
            <div className="status-header">
              <span className="status-icon">↩️</span>
              <span className="status-name">Returned</span>
            </div>
            <div className="status-stats">
              <div className="status-count">{stats.deliveriesByStatus.returned}</div>
              <div className="status-percentage">{getStatusPercentage('returned')}%</div>
            </div>
            <div className="status-bar">
              <div 
                className="status-fill returned-fill"
                style={{ width: `${getStatusPercentage('returned')}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="recent-section">
        <h3>🕒 Recent Deliveries</h3>
        {stats.recentDeliveries.length > 0 ? (
          <div className="recent-deliveries">
            {stats.recentDeliveries.map(delivery => (
              <div key={delivery.id} className="recent-delivery">
                <div className="delivery-info">
                  <div className="delivery-id">#{delivery.id}</div>
                  <div className="delivery-date">
                    {new Date(delivery.date).toLocaleDateString()}
                  </div>
                  <div className="delivery-items">{delivery.totalItems} items</div>
                </div>
                <div className={`delivery-status ${delivery.status}`}>
                  {delivery.status === 'dispatched' && '📤'}
                  {delivery.status === 'delivered' && '✅'}
                  {delivery.status === 'returned' && '↩️'}
                  {delivery.status}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-recent">No recent deliveries found</div>
        )}
      </div>

      {/* Delivery Trend */}
      {trend.length > 0 && (
        <div className="trend-section">
          <h3>📈 7-Day Delivery Trend</h3>
          <div className="trend-chart">
            {trend.map((day, index) => (
              <div key={index} className="trend-bar">
                <div 
                  className="trend-fill"
                  style={{ 
                    height: `${Math.max(10, (day.count / Math.max(...trend.map(d => d.count))) * 100)}%` 
                  }}
                ></div>
                <div className="trend-label">{day.date}</div>
                <div className="trend-value">{day.count}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      <div className="metrics-section">
        <h3>🎯 Performance Metrics</h3>
        <div className="metrics-grid">
          <div className="metric">
            <div className="metric-label">Delivery Success Rate</div>
            <div className="metric-value">
              {stats.totalDeliveries > 0 
                ? Math.round((stats.deliveriesByStatus.delivered / stats.totalDeliveries) * 100)
                : 0}%
            </div>
          </div>
          <div className="metric">
            <div className="metric-label">Return Rate</div>
            <div className="metric-value">
              {stats.totalDeliveries > 0 
                ? Math.round((stats.deliveriesByStatus.returned / stats.totalDeliveries) * 100)
                : 0}%
            </div>
          </div>
          <div className="metric">
            <div className="metric-label">Avg Items per Delivery</div>
            <div className="metric-value">
              {stats.totalDeliveries > 0 
                ? Math.round(stats.totalItemsDispatched / stats.totalDeliveries)
                : 0}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryDashboard;
