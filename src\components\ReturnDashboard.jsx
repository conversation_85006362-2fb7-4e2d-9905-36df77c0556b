import React, { useState, useEffect } from 'react';
import returnService from '../services/returnService.js';
import './ReturnDashboard.css';

const ReturnDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = () => {
    setLoading(true);
    try {
      const returnStats = returnService.getReturnStats();
      setStats(returnStats);
    } catch (error) {
      console.error('Error loading return stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="return-dashboard loading">
        <div className="loading-spinner">Loading dashboard...</div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="return-dashboard error">
        <div className="error-message">Failed to load dashboard data</div>
      </div>
    );
  }

  const getStatusPercentage = (status) => {
    if (stats.totalReturns === 0) return 0;
    return Math.round((stats.returnsByStatus[status] / stats.totalReturns) * 100);
  };

  const getTopReasons = () => {
    return Object.entries(stats.returnsByReason)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);
  };

  const getRecentActivity = () => {
    return stats.recentReturns.slice(0, 5);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getReasonDisplay = (reason) => {
    const reasonMap = {
      defective: 'Defective Product',
      wrong_item: 'Wrong Item Sent',
      damaged: 'Damaged in Transit',
      customer_change: 'Customer Changed Mind',
      quality_issue: 'Quality Issue',
      expired: 'Expired Product',
      other: 'Other'
    };
    return reasonMap[reason] || reason || 'No reason specified';
  };

  return (
    <div className="return-dashboard">
      {/* Overview Cards */}
      <div className="dashboard-overview">
        <div className="overview-card total">
          <div className="card-icon">📊</div>
          <div className="card-content">
            <div className="card-number">{stats.totalReturns}</div>
            <div className="card-label">Total Returns</div>
          </div>
        </div>

        <div className="overview-card items">
          <div className="card-icon">📦</div>
          <div className="card-content">
            <div className="card-number">{stats.totalItemsReturned}</div>
            <div className="card-label">Items Returned</div>
          </div>
        </div>

        <div className="overview-card received">
          <div className="card-icon">✅</div>
          <div className="card-content">
            <div className="card-number">{stats.returnsByStatus.received || 0}</div>
            <div className="card-label">Received</div>
            <div className="card-percentage">{getStatusPercentage('received')}%</div>
          </div>
        </div>

        <div className="overview-card pending">
          <div className="card-icon">⏳</div>
          <div className="card-content">
            <div className="card-number">{stats.returnsByStatus.pending || 0}</div>
            <div className="card-label">Pending</div>
            <div className="card-percentage">{getStatusPercentage('pending')}%</div>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="dashboard-charts">
        {/* Status Distribution */}
        <div className="chart-card">
          <h3>📊 Status Distribution</h3>
          <div className="status-chart">
            <div className="status-bar">
              <div 
                className="status-segment received"
                style={{ width: `${getStatusPercentage('received')}%` }}
                title={`Received: ${getStatusPercentage('received')}%`}
              ></div>
              <div 
                className="status-segment pending"
                style={{ width: `${getStatusPercentage('pending')}%` }}
                title={`Pending: ${getStatusPercentage('pending')}%`}
              ></div>
              <div 
                className="status-segment processed"
                style={{ width: `${getStatusPercentage('processed')}%` }}
                title={`Processed: ${getStatusPercentage('processed')}%`}
              ></div>
            </div>
            <div className="status-legend">
              <div className="legend-item">
                <span className="legend-color received"></span>
                <span>Received ({stats.returnsByStatus.received || 0})</span>
              </div>
              <div className="legend-item">
                <span className="legend-color pending"></span>
                <span>Pending ({stats.returnsByStatus.pending || 0})</span>
              </div>
              <div className="legend-item">
                <span className="legend-color processed"></span>
                <span>Processed ({stats.returnsByStatus.processed || 0})</span>
              </div>
            </div>
          </div>
        </div>

        {/* Top Return Reasons */}
        <div className="chart-card">
          <h3>🔍 Top Return Reasons</h3>
          <div className="reasons-chart">
            {getTopReasons().length > 0 ? (
              getTopReasons().map(([reason, count], index) => (
                <div key={reason} className="reason-item">
                  <div className="reason-info">
                    <span className="reason-name">{getReasonDisplay(reason)}</span>
                    <span className="reason-count">{count}</span>
                  </div>
                  <div className="reason-bar">
                    <div 
                      className="reason-fill"
                      style={{ 
                        width: `${(count / Math.max(...Object.values(stats.returnsByReason))) * 100}%`,
                        backgroundColor: `hsl(${index * 60}, 70%, 60%)`
                      }}
                    ></div>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-data">No return reasons data available</div>
            )}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="dashboard-activity">
        <div className="activity-card">
          <h3>🕒 Recent Returns</h3>
          <div className="activity-list">
            {getRecentActivity().length > 0 ? (
              getRecentActivity().map(returnItem => (
                <div key={returnItem.id} className="activity-item">
                  <div className="activity-icon">↩️</div>
                  <div className="activity-content">
                    <div className="activity-title">
                      Order #{returnItem.orderId}
                    </div>
                    <div className="activity-details">
                      {returnItem.totalItems} items • {formatDate(returnItem.date)}
                      {returnItem.reason && (
                        <> • {getReasonDisplay(returnItem.reason)}</>
                      )}
                    </div>
                  </div>
                  <div className="activity-status">
                    <span className={`status-badge status-${returnItem.status}`}>
                      {returnItem.status === 'received' && '✅'}
                      {returnItem.status === 'pending' && '⏳'}
                      {returnItem.status === 'processed' && '📋'}
                      {returnItem.status}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="no-activity">No recent returns</div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="quick-stats-card">
          <h3>📈 Quick Stats</h3>
          <div className="quick-stats">
            <div className="quick-stat">
              <div className="stat-icon">📅</div>
              <div className="stat-info">
                <div className="stat-label">Returns Today</div>
                <div className="stat-value">
                  {Object.entries(stats.returnsByDate)
                    .filter(([date]) => date === new Date().toDateString())
                    .reduce((sum, [, count]) => sum + count, 0)}
                </div>
              </div>
            </div>

            <div className="quick-stat">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <div className="stat-label">Avg Items/Return</div>
                <div className="stat-value">
                  {stats.totalReturns > 0 
                    ? Math.round((stats.totalItemsReturned / stats.totalReturns) * 10) / 10
                    : 0}
                </div>
              </div>
            </div>

            <div className="quick-stat">
              <div className="stat-icon">🔄</div>
              <div className="stat-info">
                <div className="stat-label">Processing Rate</div>
                <div className="stat-value">
                  {getStatusPercentage('processed')}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="dashboard-actions">
        <button className="btn btn-primary" onClick={loadStats}>
          🔄 Refresh Data
        </button>
      </div>
    </div>
  );
};

export default ReturnDashboard;
