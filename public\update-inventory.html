<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Inventory - <PERSON>ript</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #667eea;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 Inventory Update Tool</h1>
        
        <div class="info status">
            <strong>Ready to update inventory!</strong><br>
            This will update all product quantities with the new values you provided.
        </div>

        <div style="text-align: center;">
            <button id="updateBtn" class="btn" onclick="updateInventory()">
                🔄 Update All Product Quantities
            </button>
            <button class="btn" onclick="showSummary()" style="background: #28a745;">
                📊 Show Current Summary
            </button>
        </div>

        <div id="status"></div>
        <div id="summary"></div>

        <a href="/" class="back-link">← Back to Main Application</a>
    </div>

    <script>
        // The quantities you provided
        const newQuantities = [
            242, 644, 22, 17, 4, 7, 16, 1577, 13, 26, 21, 16, 13, 11, 14, 16, 3, -1, 1, -2,
            17, 12, 301, 0, 16, 16, 14, 40, 15, 12, 13, 4, 5, 6, 23, 17, 16, 16, 9, 9,
            34, 15, 15, 25, 5, 10, 4, 15, 0, 0, 0, 0, 1, 3, 3, 3, 0, 0, 0, 0, 0, 2,
            0, 0, 2, 3, 0, 3, 0, 0, 0, 25
        ];

        const productNames = [
            "Survana Rosemary", "O. HS (New)", "O. Argireline Solution", "O. Ascorbic Acid",
            "O. Ascorbyl Glucoside Solution", "O. Niacinamide", "O. Retinol 1%", "O. Old Hair Serum",
            "O. GA 240 ml", "O. GA 100 ml", "O. SA 2%", "O. Alpha Arbutin 2%", "O. Eye Serum",
            "O. Caffine Solution", "O. Sunscreen SPF 30", "LR-Mela B3", "LR-FS", "LR-RT", "LR-C",
            "Anua Niacinamide", "Anua Niacinamide New", "Anua Heartleaf Cleansing Oil",
            "Dr. Althea 345 Relief Cream", "Bioderma Facewash 200ml", "I'm From Rice Toner",
            "I'm From Rice Cream", "ROC Glow Serum", "Paula's Choice 118 ml", "Mielle Rosemary Oil",
            "Beauty of Joseon Rice Sunscreen", "Beauty of Joseon Glow Serum", "Beauty of Joseon Sun Stick",
            "Some BY MI Vitamin C", "Glutathione", "Cerave Night Cream", "Cerave Acnee Control Cleanser",
            "Cerave Moisturizer", "Cerave Foaming Cleanser", "Cerave Resurfacing Retinol",
            "Cerave Skin Renewing Retinol", "Cerave Skin Renewing Nightly Treatment",
            "COSRX Gentle Cleanser (Red)", "COSRX Aloe Sunscreen", "Christian Sunscreen 70ml",
            "Cotton Pad", "SKIN1004 Ampoule", "MISSHA Sun Milk", "EltaMD Sunsceeen SPF 46",
            "O. HS 30ml", "Mon Paris", "Vampire Blood", "Tam Dao", "Gucci OUD", "Tom Ford Tobacco",
            "Mont Blanc", "Hugo Boss", "Giorgio Armani", "Paco Rabanne", "Dunhill Desire",
            "Versace Eros", "Tommy Girl", "Dior Sauvage", "Nautica Voyage",
            "Gucci flora Gorgeous gardenia", "Good Girl by carolina herrera", "Davidoff cool water",
            "Dolce & gabbana the one", "creed aventus", "one million", "BLVGARI Men in Black",
            "Armani Code", "Blue de channel paris"
        ];

        function updateInventory() {
            const btn = document.getElementById('updateBtn');
            const statusDiv = document.getElementById('status');
            
            btn.disabled = true;
            btn.textContent = '🔄 Updating...';
            
            try {
                // Get existing data from localStorage
                const existingData = localStorage.getItem('inventory_products');
                if (!existingData) {
                    throw new Error('No inventory data found. Please visit the main application first to initialize the database.');
                }

                const data = JSON.parse(existingData);
                let updatedCount = 0;
                let errors = [];

                // Create a map for faster lookup
                const productMap = {};
                data.products.forEach(product => {
                    productMap[product.name] = product;
                });

                // Update quantities
                productNames.forEach((productName, index) => {
                    if (index < newQuantities.length) {
                        const newQuantity = newQuantities[index];
                        const product = productMap[productName];
                        
                        if (product) {
                            const finalQuantity = Math.max(0, newQuantity);
                            product.quantity = finalQuantity;
                            product.updatedAt = new Date().toISOString();
                            updatedCount++;
                            
                            if (newQuantity < 0) {
                                errors.push(`${productName}: negative quantity (${newQuantity}) set to 0`);
                            }
                        } else {
                            errors.push(`Product not found: ${productName}`);
                        }
                    }
                });

                // Update metadata
                data.metadata.lastUpdated = new Date().toISOString();
                data.metadata.totalProducts = data.products.length;

                // Save back to localStorage
                localStorage.setItem('inventory_products', JSON.stringify(data));

                // Show success message
                statusDiv.innerHTML = `
                    <div class="success status">
                        <strong>✅ Update Completed!</strong><br>
                        Successfully updated ${updatedCount} products.<br>
                        ${errors.length > 0 ? `<br><strong>Warnings:</strong><br>${errors.join('<br>')}` : ''}
                        <br><br>
                        <em>Refresh the main application to see the changes.</em>
                    </div>
                `;

            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="error status">
                        <strong>❌ Update Failed!</strong><br>
                        ${error.message}
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = '🔄 Update All Product Quantities';
            }
        }

        function showSummary() {
            const summaryDiv = document.getElementById('summary');
            
            try {
                const existingData = localStorage.getItem('inventory_products');
                if (!existingData) {
                    summaryDiv.innerHTML = `
                        <div class="error status">
                            No inventory data found. Please visit the main application first.
                        </div>
                    `;
                    return;
                }

                const data = JSON.parse(existingData);
                const products = data.products;
                
                const totalProducts = products.length;
                const totalQuantity = products.reduce((sum, p) => sum + p.quantity, 0);
                const outOfStock = products.filter(p => p.quantity === 0).length;
                const lowStock = products.filter(p => p.quantity > 0 && p.quantity <= 5).length;
                const inStock = products.filter(p => p.quantity > 5).length;

                summaryDiv.innerHTML = `
                    <div class="summary">
                        <h3>📊 Current Inventory Summary</h3>
                        <p><strong>Total Products:</strong> ${totalProducts}</p>
                        <p><strong>Total Quantity:</strong> ${totalQuantity} units</p>
                        <p><strong>In Stock (>5):</strong> ${inStock} products</p>
                        <p><strong>Low Stock (1-5):</strong> ${lowStock} products</p>
                        <p><strong>Out of Stock:</strong> ${outOfStock} products</p>
                        <p><strong>Last Updated:</strong> ${new Date(data.metadata.lastUpdated).toLocaleString()}</p>
                    </div>
                `;

            } catch (error) {
                summaryDiv.innerHTML = `
                    <div class="error status">
                        Error reading inventory data: ${error.message}
                    </div>
                `;
            }
        }

        // Show summary on page load
        window.onload = function() {
            showSummary();
        };
    </script>
</body>
</html>
