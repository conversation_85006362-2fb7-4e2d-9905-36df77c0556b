import React, { useState, useEffect } from 'react';
import { useProducts } from '../hooks/useProducts.js';
import purchaseService from '../services/purchaseService.js';
import './PurchaseForm.css';

const PurchaseForm = ({ isOpen, onClose, onPurchaseCreated, editingPurchase = null }) => {
  const { products } = useProducts();
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    items: [{ itemName: '', unitPrice: '', quantity: '', totalPrice: 0 }],
    notes: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when editing
  useEffect(() => {
    if (editingPurchase) {
      setFormData({
        date: editingPurchase.date,
        items: editingPurchase.items.map(item => ({
          itemName: item.itemName,
          unitPrice: item.unitPrice.toString(),
          quantity: item.quantity.toString(),
          totalPrice: item.totalPrice
        })),
        notes: editingPurchase.notes || ''
      });
    } else {
      // Reset form for new purchase
      setFormData({
        date: new Date().toISOString().split('T')[0],
        items: [{ itemName: '', unitPrice: '', quantity: '', totalPrice: 0 }],
        notes: ''
      });
    }
    setErrors({});
  }, [editingPurchase, isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (itemIndex, field, value) => {
    const newItems = [...formData.items];
    newItems[itemIndex] = {
      ...newItems[itemIndex],
      [field]: value
    };

    // Auto-calculate total price when unit price or quantity changes
    if (field === 'unitPrice' || field === 'quantity') {
      const unitPrice = parseFloat(newItems[itemIndex].unitPrice) || 0;
      const quantity = parseInt(newItems[itemIndex].quantity) || 0;
      newItems[itemIndex].totalPrice = unitPrice * quantity;
    }

    setFormData(prev => ({
      ...prev,
      items: newItems
    }));

    // Clear errors for this item
    const newErrors = { ...errors };
    delete newErrors[`item_${itemIndex}_${field}`];
    setErrors(newErrors);
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { itemName: '', unitPrice: '', quantity: '', totalPrice: 0 }]
    }));
  };

  const removeItem = (itemIndex) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, index) => index !== itemIndex);
      setFormData(prev => ({
        ...prev,
        items: newItems
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = 'Purchase date is required';
    }

    // Validate items
    formData.items.forEach((item, itemIndex) => {
      if (!item.itemName.trim()) {
        newErrors[`item_${itemIndex}_itemName`] = 'Item name is required';
      }
      if (!item.unitPrice || parseFloat(item.unitPrice) <= 0) {
        newErrors[`item_${itemIndex}_unitPrice`] = 'Valid unit price is required';
      }
      if (!item.quantity || parseInt(item.quantity) <= 0) {
        newErrors[`item_${itemIndex}_quantity`] = 'Valid quantity is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const purchaseData = {
        ...formData,
        items: formData.items.map(item => ({
          itemName: item.itemName.trim(),
          unitPrice: parseFloat(item.unitPrice),
          quantity: parseInt(item.quantity),
          totalPrice: parseFloat(item.unitPrice) * parseInt(item.quantity)
        }))
      };

      let result;
      if (editingPurchase) {
        result = await purchaseService.updatePurchase(editingPurchase.id, purchaseData);
      } else {
        result = await purchaseService.createPurchase(purchaseData);
      }

      onPurchaseCreated(result);
      onClose();
    } catch (error) {
      console.error('Error saving purchase:', error);
      setErrors({ submit: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTotalAmount = () => {
    return formData.items.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
  };

  const getTotalQuantity = () => {
    return formData.items.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content purchase-form-modal">
        <div className="modal-header">
          <h2>{editingPurchase ? 'Edit Purchase' : 'New Purchase'}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="purchase-form">
          {/* Purchase Date */}
          <div className="form-group">
            <label htmlFor="date">Purchase Date *</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className={errors.date ? 'error' : ''}
              required
            />
            {errors.date && <span className="error-text">{errors.date}</span>}
          </div>

          {/* Items Section */}
          <div className="items-section">
            <div className="section-header">
              <h3>📦 Purchase Items</h3>
              <button type="button" className="btn btn-primary" onClick={addItem}>
                + Add Item
              </button>
            </div>

            {formData.items.map((item, itemIndex) => (
              <div key={itemIndex} className="item-card">
                <div className="item-header">
                  <h4>Item #{itemIndex + 1}</h4>
                  {formData.items.length > 1 && (
                    <button
                      type="button"
                      className="remove-item-btn"
                      onClick={() => removeItem(itemIndex)}
                    >
                      🗑️ Remove
                    </button>
                  )}
                </div>

                <div className="item-fields">
                  {/* Item Name */}
                  <div className="form-group">
                    <label htmlFor={`itemName_${itemIndex}`}>Item Name *</label>
                    <input
                      type="text"
                      id={`itemName_${itemIndex}`}
                      value={item.itemName}
                      onChange={(e) => handleItemChange(itemIndex, 'itemName', e.target.value)}
                      className={errors[`item_${itemIndex}_itemName`] ? 'error' : ''}
                      placeholder="Enter item name..."
                      list={`products-list-${itemIndex}`}
                      required
                    />
                    <datalist id={`products-list-${itemIndex}`}>
                      {products.map(product => (
                        <option key={product.id} value={product.name} />
                      ))}
                    </datalist>
                    {errors[`item_${itemIndex}_itemName`] && (
                      <span className="error-text">{errors[`item_${itemIndex}_itemName`]}</span>
                    )}
                  </div>

                  {/* Unit Price */}
                  <div className="form-group">
                    <label htmlFor={`unitPrice_${itemIndex}`}>Unit Price *</label>
                    <input
                      type="number"
                      id={`unitPrice_${itemIndex}`}
                      value={item.unitPrice}
                      onChange={(e) => handleItemChange(itemIndex, 'unitPrice', e.target.value)}
                      className={errors[`item_${itemIndex}_unitPrice`] ? 'error' : ''}
                      placeholder="0.00"
                      step="0.01"
                      min="0"
                      required
                    />
                    {errors[`item_${itemIndex}_unitPrice`] && (
                      <span className="error-text">{errors[`item_${itemIndex}_unitPrice`]}</span>
                    )}
                  </div>

                  {/* Quantity */}
                  <div className="form-group">
                    <label htmlFor={`quantity_${itemIndex}`}>Quantity *</label>
                    <input
                      type="number"
                      id={`quantity_${itemIndex}`}
                      value={item.quantity}
                      onChange={(e) => handleItemChange(itemIndex, 'quantity', e.target.value)}
                      className={errors[`item_${itemIndex}_quantity`] ? 'error' : ''}
                      placeholder="0"
                      min="1"
                      required
                    />
                    {errors[`item_${itemIndex}_quantity`] && (
                      <span className="error-text">{errors[`item_${itemIndex}_quantity`]}</span>
                    )}
                  </div>

                  {/* Total Price (Read-only) */}
                  <div className="form-group">
                    <label>Total Price</label>
                    <div className="total-price-display">
                      ${item.totalPrice.toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Purchase Summary */}
          <div className="purchase-summary">
            <div className="summary-row">
              <span>Total Items:</span>
              <span>{getTotalQuantity()}</span>
            </div>
            <div className="summary-row total">
              <span>Total Amount:</span>
              <span>${getTotalAmount().toFixed(2)}</span>
            </div>
          </div>

          {/* Notes */}
          <div className="form-group">
            <label htmlFor="notes">Notes (Optional)</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              placeholder="Add any additional notes about this purchase..."
              rows="3"
            />
          </div>

          {/* Submit Error */}
          {errors.submit && (
            <div className="error-banner">
              <span className="error-icon">❌</span>
              <span className="error-text">{errors.submit}</span>
            </div>
          )}

          {/* Form Actions */}
          <div className="form-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : (editingPurchase ? 'Update Purchase' : 'Create Purchase')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PurchaseForm;
