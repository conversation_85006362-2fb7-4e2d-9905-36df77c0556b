import React, { useState } from 'react';
import { usePurchases } from '../hooks/usePurchases.js';
import PurchaseForm from './PurchaseForm.jsx';
import PurchaseList from './PurchaseList.jsx';
import { useAuth, PermissionWrapper } from '../hooks/useAuth.js';
import './PurchasePage.css';

const PurchasePage = () => {
  const {
    purchases,
    loading,
    error,
    createPurchase,
    updatePurchase,
    deletePurchase,
    searchPurchases,
    filterPurchasesByDateRange,
    getPurchaseStats,
    getInventoryValue,
    refreshPurchases
  } = usePurchases();

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPurchase, setEditingPurchase] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState({ start: '', end: '' });
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'analytics'
  const [filteredPurchases, setFilteredPurchases] = useState(purchases);

  const { hasPermission } = useAuth();

  // Update filtered purchases when purchases, search, or date filter changes
  React.useEffect(() => {
    let filtered = purchases;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = searchPurchases(searchQuery);
    }

    // Apply date filter
    if (dateFilter.start && dateFilter.end) {
      filtered = filterPurchasesByDateRange(dateFilter.start, dateFilter.end);
    }

    setFilteredPurchases(filtered);
  }, [purchases, searchQuery, dateFilter, searchPurchases, filterPurchasesByDateRange]);

  const handleCreatePurchase = () => {
    setEditingPurchase(null);
    setIsFormOpen(true);
  };

  const handleEditPurchase = (purchase) => {
    setEditingPurchase(purchase);
    setIsFormOpen(true);
  };

  const handlePurchaseCreated = async (purchase) => {
    console.log('Purchase created:', purchase);
    await refreshPurchases();
  };

  const handleDeletePurchase = async (id) => {
    try {
      await deletePurchase(id);
    } catch (error) {
      console.error('Error deleting purchase:', error);
    }
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleDateFilterChange = (field, value) => {
    setDateFilter(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    setSearchQuery('');
    setDateFilter({ start: '', end: '' });
  };

  const formatCurrency = (amount) => {
    return `৳${amount.toFixed(2)}`;
  };

  const stats = getPurchaseStats();
  const inventoryValue = getInventoryValue();

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading purchases...</p>
      </div>
    );
  }

  return (
    <div className="purchase-page">
      <div className="page-header">
        <div className="header-content">
          <h1>📦 Purchase Management</h1>
          <p>Track your inventory purchases and manage stock levels</p>
        </div>
        <div className="header-actions">
          <button
            className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setViewMode('list')}
          >
            📋 List View
          </button>
          <button
            className={`btn ${viewMode === 'analytics' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setViewMode('analytics')}
          >
            📊 Analytics
          </button>
          <PermissionWrapper permission="canCreate">
            <button className="btn btn-primary" onClick={handleCreatePurchase}>
              ➕ New Purchase
            </button>
          </PermissionWrapper>
        </div>
      </div>

      {error && (
        <div className="error-banner">
          <span className="error-icon">❌</span>
          <span>{error}</span>
        </div>
      )}

      {viewMode === 'analytics' && (
        <div className="analytics-section">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">📦</div>
              <div className="stat-content">
                <div className="stat-value">{stats.totalPurchases}</div>
                <div className="stat-label">Total Purchases</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">💳</div>
              <div className="stat-content">
                <div className="stat-value">{formatCurrency(stats.totalAmount)}</div>
                <div className="stat-label">Total Spent</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-content">
                <div className="stat-value">{stats.totalQuantity}</div>
                <div className="stat-label">Items Purchased</div>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🏦</div>
              <div className="stat-content">
                <div className="stat-value">{formatCurrency(inventoryValue)}</div>
                <div className="stat-label">Inventory Value</div>
              </div>
            </div>
          </div>

          {/* Top Items */}
          <div className="analytics-card">
            <h3>🏆 Top Purchased Items</h3>
            <div className="top-items-grid">
              {Object.entries(stats.topItems)
                .sort(([,a], [,b]) => b.amount - a.amount)
                .slice(0, 6)
                .map(([itemName, data]) => (
                  <div key={itemName} className="top-item">
                    <div className="item-name">{itemName}</div>
                    <div className="item-stats">
                      <span className="quantity">{data.quantity} units</span>
                      <span className="amount">{formatCurrency(data.amount)}</span>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Monthly Purchases */}
          <div className="analytics-card">
            <h3>📅 Monthly Purchase Summary</h3>
            <div className="monthly-grid">
              {Object.entries(stats.purchasesByMonth)
                .sort(([a], [b]) => b.localeCompare(a))
                .slice(0, 6)
                .map(([month, data]) => (
                  <div key={month} className="monthly-item">
                    <div className="month-name">{new Date(month + '-01').toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}</div>
                    <div className="month-stats">
                      <span className="count">{data.count} purchases</span>
                      <span className="amount">{formatCurrency(data.amount)}</span>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {viewMode === 'list' && (
        <div className="list-section">
          {/* Filters */}
          <div className="filters-section">
            <div className="search-filter">
              <input
                type="text"
                placeholder="Search purchases..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="search-input"
              />
            </div>
            <div className="date-filters">
              <input
                type="date"
                value={dateFilter.start}
                onChange={(e) => handleDateFilterChange('start', e.target.value)}
                className="date-input"
                placeholder="Start date"
              />
              <input
                type="date"
                value={dateFilter.end}
                onChange={(e) => handleDateFilterChange('end', e.target.value)}
                className="date-input"
                placeholder="End date"
              />
              <button className="btn btn-secondary" onClick={clearFilters}>
                Clear Filters
              </button>
            </div>
          </div>

          {/* Purchase List */}
          <PurchaseList
            purchases={filteredPurchases}
            onEditPurchase={handleEditPurchase}
            onDeletePurchase={handleDeletePurchase}
            onRefresh={refreshPurchases}
          />
        </div>
      )}

      {/* Purchase Form Modal */}
      <PermissionWrapper permission="canCreate">
        <PurchaseForm
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          onPurchaseCreated={handlePurchaseCreated}
          editingPurchase={editingPurchase}
        />
      </PermissionWrapper>
    </div>
  );
};

export default PurchasePage;
