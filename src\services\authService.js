// Authentication Service for Role-Based Access Control
const AUTH_STORAGE_KEY = 'inventory_auth';
const USERS_STORAGE_KEY = 'inventory_users';

// User roles
export const USER_ROLES = {
  ADMIN: 'admin',
  VIEWER: 'viewer'
};

// Permissions for each role
export const PERMISSIONS = {
  [USER_ROLES.ADMIN]: {
    canView: true,
    canCreate: true,
    canEdit: true,
    canDelete: true,
    canManageUsers: true,
    canAccessAdmin: true
  },
  [USER_ROLES.VIEWER]: {
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false,
    canManageUsers: false,
    canAccessAdmin: false
  }
};

class AuthService {
  constructor() {
    this.initializeDefaultUsers();
  }

  // Initialize default users if none exist
  initializeDefaultUsers() {
    const users = this.getAllUsers();
    if (users.length === 0) {
      const defaultUsers = [
        {
          id: 'admin-001',
          username: 'admin',
          password: 'admin123', // In production, this should be hashed
          role: USER_ROLES.ADMIN,
          name: 'Administrator',
          email: '<EMAIL>',
          createdAt: new Date().toISOString(),
          isActive: true
        },
        {
          id: 'viewer-001',
          username: 'viewer',
          password: 'viewer123', // In production, this should be hashed
          role: USER_ROLES.VIEWER,
          name: 'Viewer User',
          email: '<EMAIL>',
          createdAt: new Date().toISOString(),
          isActive: true
        }
      ];

      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify({
        users: defaultUsers,
        metadata: {
          version: '1.0',
          lastUpdated: new Date().toISOString()
        }
      }));
    }
  }

  // Get all users
  getAllUsers() {
    try {
      const data = JSON.parse(localStorage.getItem(USERS_STORAGE_KEY));
      return data?.users || [];
    } catch (error) {
      console.error('Error loading users:', error);
      return [];
    }
  }

  // Login user
  login(username, password) {
    try {
      const users = this.getAllUsers();
      const user = users.find(u => 
        u.username === username && 
        u.password === password && 
        u.isActive
      );

      if (!user) {
        throw new Error('Invalid username or password');
      }

      // Create session
      const session = {
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          name: user.name,
          email: user.email
        },
        loginTime: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      };

      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(session));
      
      // Dispatch login event
      window.dispatchEvent(new CustomEvent('userLoggedIn', {
        detail: { user: session.user }
      }));

      return session.user;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout user
  logout() {
    try {
      localStorage.removeItem(AUTH_STORAGE_KEY);
      
      // Dispatch logout event
      window.dispatchEvent(new CustomEvent('userLoggedOut'));
      
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  }

  // Get current user session
  getCurrentUser() {
    try {
      const session = JSON.parse(localStorage.getItem(AUTH_STORAGE_KEY));
      
      if (!session) {
        return null;
      }

      // Check if session is expired
      if (new Date() > new Date(session.expiresAt)) {
        this.logout();
        return null;
      }

      return session.user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return this.getCurrentUser() !== null;
  }

  // Check if user has specific permission
  hasPermission(permission) {
    const user = this.getCurrentUser();
    if (!user) return false;

    const userPermissions = PERMISSIONS[user.role];
    return userPermissions && userPermissions[permission];
  }

  // Check if user is admin
  isAdmin() {
    const user = this.getCurrentUser();
    return user && user.role === USER_ROLES.ADMIN;
  }

  // Check if user is viewer
  isViewer() {
    const user = this.getCurrentUser();
    return user && user.role === USER_ROLES.VIEWER;
  }

  // Add new user (admin only)
  addUser(userData) {
    if (!this.hasPermission('canManageUsers')) {
      throw new Error('Permission denied: Cannot manage users');
    }

    try {
      const data = JSON.parse(localStorage.getItem(USERS_STORAGE_KEY));
      const users = data?.users || [];

      // Check if username already exists
      if (users.find(u => u.username === userData.username)) {
        throw new Error('Username already exists');
      }

      const newUser = {
        id: 'user-' + Date.now(),
        username: userData.username,
        password: userData.password, // In production, hash this
        role: userData.role,
        name: userData.name,
        email: userData.email,
        createdAt: new Date().toISOString(),
        isActive: true
      };

      users.push(newUser);
      
      const updatedData = {
        users: users,
        metadata: {
          version: '1.0',
          lastUpdated: new Date().toISOString()
        }
      };

      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(updatedData));
      
      return newUser;
    } catch (error) {
      console.error('Error adding user:', error);
      throw error;
    }
  }

  // Update user (admin only)
  updateUser(userId, updates) {
    if (!this.hasPermission('canManageUsers')) {
      throw new Error('Permission denied: Cannot manage users');
    }

    try {
      const data = JSON.parse(localStorage.getItem(USERS_STORAGE_KEY));
      const users = data?.users || [];

      const userIndex = users.findIndex(u => u.id === userId);
      if (userIndex === -1) {
        throw new Error('User not found');
      }

      // Don't allow changing username if it conflicts
      if (updates.username && updates.username !== users[userIndex].username) {
        if (users.find(u => u.username === updates.username && u.id !== userId)) {
          throw new Error('Username already exists');
        }
      }

      users[userIndex] = {
        ...users[userIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const updatedData = {
        users: users,
        metadata: {
          version: '1.0',
          lastUpdated: new Date().toISOString()
        }
      };

      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(updatedData));
      
      return users[userIndex];
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Delete user (admin only)
  deleteUser(userId) {
    if (!this.hasPermission('canManageUsers')) {
      throw new Error('Permission denied: Cannot manage users');
    }

    try {
      const currentUser = this.getCurrentUser();
      if (currentUser && currentUser.id === userId) {
        throw new Error('Cannot delete your own account');
      }

      const data = JSON.parse(localStorage.getItem(USERS_STORAGE_KEY));
      const users = data?.users || [];

      const filteredUsers = users.filter(u => u.id !== userId);
      
      const updatedData = {
        users: filteredUsers,
        metadata: {
          version: '1.0',
          lastUpdated: new Date().toISOString()
        }
      };

      localStorage.setItem(USERS_STORAGE_KEY, JSON.stringify(updatedData));
      
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }
}

// Create singleton instance
const authService = new AuthService();
export default authService;
