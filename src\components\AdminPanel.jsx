import React, { useState } from 'react';
import { useAuth, PermissionWrapper, RoleWrapper } from '../hooks/useAuth.js';
import UserManagement from './UserManagement.jsx';
import './AdminPanel.css';

const AdminPanel = ({ onClose }) => {
  const { user, logout, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  const handleLogout = () => {
    logout();
    if (onClose) {
      onClose();
    }
  };

  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      permission: 'canView'
    },
    {
      id: 'users',
      label: 'User Management',
      icon: '👥',
      permission: 'canManageUsers'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      permission: 'canAccessAdmin'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'users':
        return <UserManagement />;
      case 'settings':
        return <AdminSettings />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <div className="admin-panel">
      <div className="admin-header">
        <div className="admin-title">
          <h1>🛡️ Admin Panel</h1>
          <p>System Administration & Management</p>
        </div>
        
        <div className="admin-user-info">
          <div className="user-details">
            <span className="user-name">{user?.name}</span>
            <span className="user-role">{user?.role}</span>
          </div>
          <button className="logout-button" onClick={handleLogout}>
            <span className="logout-icon">🚪</span>
            Logout
          </button>
        </div>
      </div>

      <div className="admin-content">
        <div className="admin-sidebar">
          <nav className="admin-nav">
            {tabs.map(tab => (
              <PermissionWrapper key={tab.id} permission={tab.permission}>
                <button
                  className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <span className="nav-icon">{tab.icon}</span>
                  <span className="nav-label">{tab.label}</span>
                </button>
              </PermissionWrapper>
            ))}
          </nav>
        </div>

        <div className="admin-main">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

// Admin Dashboard Component
const AdminDashboard = () => {
  const { user, isAdmin } = useAuth();

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h2>Welcome, {user?.name}!</h2>
        <p>Here's an overview of your system access and permissions.</p>
      </div>

      <div className="dashboard-cards">
        <div className="dashboard-card">
          <div className="card-icon">👤</div>
          <div className="card-content">
            <h3>Your Role</h3>
            <p className={`role-badge ${user?.role}`}>
              {user?.role === 'admin' ? '👑 Administrator' : '👀 Viewer'}
            </p>
          </div>
        </div>

        <div className="dashboard-card">
          <div className="card-icon">🔐</div>
          <div className="card-content">
            <h3>Access Level</h3>
            <p>{isAdmin() ? 'Full System Access' : 'Read-Only Access'}</p>
          </div>
        </div>

        <div className="dashboard-card">
          <div className="card-icon">📧</div>
          <div className="card-content">
            <h3>Email</h3>
            <p>{user?.email}</p>
          </div>
        </div>

        <div className="dashboard-card">
          <div className="card-icon">🕒</div>
          <div className="card-content">
            <h3>Last Login</h3>
            <p>Current Session</p>
          </div>
        </div>
      </div>

      <div className="permissions-section">
        <h3>Your Permissions</h3>
        <div className="permissions-grid">
          <PermissionItem permission="canView" label="View Data" icon="👁️" />
          <PermissionItem permission="canCreate" label="Create Records" icon="➕" />
          <PermissionItem permission="canEdit" label="Edit Records" icon="✏️" />
          <PermissionItem permission="canDelete" label="Delete Records" icon="🗑️" />
          <PermissionItem permission="canManageUsers" label="Manage Users" icon="👥" />
          <PermissionItem permission="canAccessAdmin" label="Admin Access" icon="🛡️" />
        </div>
      </div>
    </div>
  );
};

// Permission Item Component
const PermissionItem = ({ permission, label, icon }) => {
  const { hasPermission } = useAuth();
  const allowed = hasPermission(permission);

  return (
    <div className={`permission-item ${allowed ? 'allowed' : 'denied'}`}>
      <span className="permission-icon">{icon}</span>
      <span className="permission-label">{label}</span>
      <span className="permission-status">
        {allowed ? '✅' : '❌'}
      </span>
    </div>
  );
};

// Admin Settings Component
const AdminSettings = () => {
  return (
    <div className="admin-settings">
      <div className="settings-header">
        <h2>System Settings</h2>
        <p>Configure system-wide settings and preferences.</p>
      </div>

      <div className="settings-sections">
        <div className="settings-section">
          <h3>🔒 Security Settings</h3>
          <div className="setting-item">
            <label>Session Timeout</label>
            <select defaultValue="24">
              <option value="1">1 Hour</option>
              <option value="8">8 Hours</option>
              <option value="24">24 Hours</option>
              <option value="168">1 Week</option>
            </select>
          </div>
          <div className="setting-item">
            <label>
              <input type="checkbox" defaultChecked />
              Require password change on first login
            </label>
          </div>
        </div>

        <div className="settings-section">
          <h3>📊 System Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Version:</strong> 1.0.0
            </div>
            <div className="info-item">
              <strong>Database:</strong> LocalStorage
            </div>
            <div className="info-item">
              <strong>Last Backup:</strong> Not configured
            </div>
            <div className="info-item">
              <strong>Storage Used:</strong> {Math.round(JSON.stringify(localStorage).length / 1024)} KB
            </div>
          </div>
        </div>

        <RoleWrapper roles={['admin']}>
          <div className="settings-section danger-zone">
            <h3>⚠️ Danger Zone</h3>
            <p>These actions cannot be undone. Please be careful.</p>
            <div className="danger-actions">
              <button className="danger-button" onClick={() => {
                if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                  localStorage.clear();
                  window.location.reload();
                }
              }}>
                🗑️ Clear All Data
              </button>
            </div>
          </div>
        </RoleWrapper>
      </div>
    </div>
  );
};

export default AdminPanel;
