/* Admin Panel Styles */
.admin-panel {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.admin-title h1 {
  margin: 0 0 5px 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.admin-title p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-name {
  font-weight: 600;
  font-size: 1rem;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
  text-transform: capitalize;
}

.logout-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.admin-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.admin-sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid #e1e5e9;
  padding: 20px 0;
}

.admin-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #666;
  font-size: 0.95rem;
}

.nav-item:hover {
  background-color: #f8f9fa;
  color: #333;
}

.nav-item.active {
  background-color: #667eea;
  color: white;
  border-right: 3px solid #5a67d8;
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-label {
  font-weight: 500;
}

.admin-main {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* Dashboard Styles */
.admin-dashboard {
  max-width: 1200px;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h2 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.dashboard-header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.dashboard-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.card-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.card-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.role-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.8rem;
}

.role-badge.admin {
  background-color: #d4edda;
  color: #155724;
}

.role-badge.viewer {
  background-color: #d1ecf1;
  color: #0c5460;
}

.permissions-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.permissions-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.permission-item.allowed {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.permission-item.denied {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.permission-icon {
  font-size: 1.1rem;
}

.permission-label {
  flex: 1;
  font-weight: 500;
  font-size: 0.9rem;
}

.permission-status {
  font-size: 1rem;
}

/* Settings Styles */
.admin-settings {
  max-width: 800px;
}

.settings-header {
  margin-bottom: 30px;
}

.settings-header h2 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.settings-header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.setting-item select {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  font-size: 0.9rem;
}

.danger-zone {
  border: 2px solid #dc3545 !important;
  background-color: #fff5f5 !important;
}

.danger-zone h3 {
  color: #dc3545 !important;
}

.danger-actions {
  margin-top: 16px;
}

.danger-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.danger-button:hover {
  background-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .admin-content {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    padding: 15px 0;
  }
  
  .admin-nav {
    flex-direction: row;
    overflow-x: auto;
    padding: 0 20px;
  }
  
  .nav-item {
    white-space: nowrap;
    min-width: 120px;
  }
  
  .admin-main {
    padding: 20px;
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
  
  .permissions-grid {
    grid-template-columns: 1fr;
  }
}
