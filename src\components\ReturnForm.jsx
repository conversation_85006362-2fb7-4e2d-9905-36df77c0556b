import React, { useState, useEffect } from 'react';
import { useProducts } from '../hooks/useProducts.js';
import returnService from '../services/returnService.js';
import './ReturnForm.css';

const ReturnForm = ({ isOpen, onClose, onReturnCreated, editingReturn = null }) => {
  const { products } = useProducts();

  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    orders: [
      {
        orderId: '',
        items: [{ productId: '', productName: '', quantity: 1 }],
        reason: '',
        notes: ''
      }
    ],
    status: 'received'
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (editingReturn) {
        // Convert single return back to the new format for editing
        setFormData({
          date: editingReturn.date,
          orders: [
            {
              orderId: editingReturn.orderId,
              items: editingReturn.items || [{ productId: '', productName: '', quantity: 1 }],
              reason: editingReturn.reason || '',
              notes: editingReturn.notes || ''
            }
          ],
          status: editingReturn.status || 'received'
        });
      } else {
        setFormData({
          date: new Date().toISOString().split('T')[0],
          orders: [
            {
              orderId: '',
              items: [{ productId: '', productName: '', quantity: 1 }],
              reason: '',
              notes: ''
            }
          ],
          status: 'received'
        });
      }
      setErrors({});
    }
  }, [isOpen, editingReturn]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleOrderChange = (orderIndex, field, value) => {
    const newOrders = [...formData.orders];
    newOrders[orderIndex] = {
      ...newOrders[orderIndex],
      [field]: value
    };
    setFormData(prev => ({
      ...prev,
      orders: newOrders
    }));

    // Real-time validation for order ID duplicates
    if (field === 'orderId') {
      const newErrors = { ...errors };

      // Clear previous errors for this order
      delete newErrors[`order_${orderIndex}_id`];
      delete newErrors.duplicateOrderIds;

      if (value.trim()) {
        // Check for duplicates within current form
        const currentOrderIds = newOrders.map(order => order.orderId.trim()).filter(id => id !== '');
        const duplicatesInForm = currentOrderIds.filter((id, index) => currentOrderIds.indexOf(id) !== index);

        if (duplicatesInForm.includes(value.trim())) {
          newErrors[`order_${orderIndex}_id`] = 'Duplicate Order ID in this form';
        }

        // Check if order ID already exists in database
        const existingReturns = returnService.getReturnsByOrderId(value.trim());
        const hasExistingReturn = existingReturns.some(existingReturn =>
          !editingReturn || existingReturn.id !== editingReturn.id
        );

        if (hasExistingReturn && !newErrors[`order_${orderIndex}_id`]) {
          newErrors[`order_${orderIndex}_id`] = `Order ID "${value}" already exists in returns`;
        }
      }

      setErrors(newErrors);
    }
  };

  const handleItemChange = (orderIndex, itemIndex, field, value) => {
    const newOrders = [...formData.orders];
    const newItems = [...newOrders[orderIndex].items];
    newItems[itemIndex] = {
      ...newItems[itemIndex],
      [field]: field === 'quantity' ? parseInt(value) || 0 : value
    };

    // Add product name for display
    if (field === 'productId' && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        newItems[itemIndex].productName = product.name;
      }
    }

    newOrders[orderIndex].items = newItems;
    setFormData(prev => ({
      ...prev,
      orders: newOrders
    }));
  };

  const addOrder = () => {
    setFormData(prev => ({
      ...prev,
      orders: [...prev.orders, {
        orderId: '',
        items: [{ productId: '', productName: '', quantity: 1 }],
        reason: '',
        notes: ''
      }]
    }));
  };

  const removeOrder = (orderIndex) => {
    if (formData.orders.length > 1) {
      setFormData(prev => ({
        ...prev,
        orders: prev.orders.filter((_, i) => i !== orderIndex)
      }));
    }
  };

  const addItem = (orderIndex) => {
    const newOrders = [...formData.orders];
    newOrders[orderIndex].items.push({ productId: '', productName: '', quantity: 1 });
    setFormData(prev => ({
      ...prev,
      orders: newOrders
    }));
  };

  const removeItem = (orderIndex, itemIndex) => {
    const newOrders = [...formData.orders];
    if (newOrders[orderIndex].items.length > 1) {
      newOrders[orderIndex].items = newOrders[orderIndex].items.filter((_, i) => i !== itemIndex);
      setFormData(prev => ({
        ...prev,
        orders: newOrders
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = 'Return date is required';
    }

    // Check for duplicate order IDs
    const orderIds = formData.orders.map(order => order.orderId.trim()).filter(id => id !== '');
    const duplicateIds = orderIds.filter((id, index) => orderIds.indexOf(id) !== index);

    if (duplicateIds.length > 0) {
      newErrors.duplicateOrderIds = `Duplicate Order IDs found: ${[...new Set(duplicateIds)].join(', ')}`;
    }

    // Validate orders
    formData.orders.forEach((order, orderIndex) => {
      if (!order.orderId.trim()) {
        newErrors[`order_${orderIndex}_id`] = 'Order ID is required';
      } else {
        // Check if this order ID already exists in the database (excluding current edit)
        const existingReturns = returnService.getReturnsByOrderId(order.orderId.trim());
        const hasExistingReturn = existingReturns.some(existingReturn =>
          !editingReturn || existingReturn.id !== editingReturn.id
        );

        if (hasExistingReturn) {
          newErrors[`order_${orderIndex}_id`] = `Order ID "${order.orderId}" already exists in returns`;
        }
      }

      // Validate items for each order
      order.items.forEach((item, itemIndex) => {
        if (!item.productId) {
          newErrors[`order_${orderIndex}_item_${itemIndex}_product`] = 'Product is required';
        }
        if (!item.quantity || item.quantity <= 0) {
          newErrors[`order_${orderIndex}_item_${itemIndex}_quantity`] = 'Valid quantity is required';
        }
      });
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Create separate returns for each order
      const results = [];

      for (const order of formData.orders) {
        // Prepare items with product names
        const itemsWithNames = order.items.map(item => {
          const product = products.find(p => p.id === item.productId);
          return {
            ...item,
            productName: product ? product.name : 'Unknown Product'
          };
        });

        const returnData = {
          orderId: order.orderId,
          date: formData.date,
          items: itemsWithNames,
          reason: order.reason,
          status: formData.status,
          notes: order.notes
        };

        let result;
        if (editingReturn && formData.orders.length === 1) {
          // If editing and only one order, update the existing return
          result = returnService.updateReturn(editingReturn.id, returnData);
        } else {
          // Create new return
          result = returnService.createReturn(returnData);
        }
        results.push(result);
      }

      onReturnCreated(results);
      onClose();
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content return-form-modal">
        <div className="modal-header">
          <h2>{editingReturn ? '📝 Edit Return' : '↩️ New Return'}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="return-form" style={{ padding: '1.5rem' }}>
          {/* Return Date */}
          <div className="form-group">
            <label htmlFor="date">Return Date *</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className={errors.date ? 'error' : ''}
              required
            />
            {errors.date && <span className="error-text">{errors.date}</span>}
          </div>

          {/* Duplicate Order IDs Error */}
          {errors.duplicateOrderIds && (
            <div className="error-banner">
              <span className="error-icon">⚠️</span>
              <span className="error-text">{errors.duplicateOrderIds}</span>
            </div>
          )}

          {/* Orders Section */}
          <div className="orders-section">
            <div className="section-header">
              <h3>📋 Return Orders for {new Date(formData.date).toLocaleDateString()}</h3>
              <button type="button" className="btn btn-primary" onClick={addOrder}>
                + Add Order
              </button>
            </div>
            <p className="helper-text">
              📝 <strong>Important:</strong> Each order ID must be unique. You cannot use the same order ID twice.
            </p>

            {formData.orders.map((order, orderIndex) => (
              <div key={orderIndex} className="order-section">
                <div className="order-header">
                  <div className="order-title">
                    <h4>Order #{orderIndex + 1}</h4>
                    {formData.orders.length > 1 && (
                      <button
                        type="button"
                        className="remove-order-btn"
                        onClick={() => removeOrder(orderIndex)}
                        title="Remove order"
                      >
                        🗑️ Remove Order
                      </button>
                    )}
                  </div>
                </div>

                {/* Order ID */}
                <div className="form-group">
                  <label htmlFor={`orderId_${orderIndex}`}>Order ID *
                    {order.orderId && !errors[`order_${orderIndex}_id`] && (
                      <span className="validation-success">✓</span>
                    )}
                  </label>
                  <input
                    type="text"
                    id={`orderId_${orderIndex}`}
                    value={order.orderId}
                    onChange={(e) => handleOrderChange(orderIndex, 'orderId', e.target.value)}
                    className={errors[`order_${orderIndex}_id`] ? 'error' : (order.orderId && !errors[`order_${orderIndex}_id`] ? 'success' : '')}
                    placeholder="Enter unique order ID..."
                    required
                  />
                  {errors[`order_${orderIndex}_id`] && (
                    <span className="error-text">
                      <span className="error-icon">❌</span>
                      {errors[`order_${orderIndex}_id`]}
                    </span>
                  )}
                </div>

                {/* Items for this order */}
                <div className="items-subsection">
                  <div className="subsection-header">
                    <h5>📦 Items in Order {order.orderId || `#${orderIndex + 1}`}</h5>
                    <button
                      type="button"
                      className="btn btn-secondary btn-sm"
                      onClick={() => addItem(orderIndex)}
                    >
                      + Add Item
                    </button>
                  </div>

                  {order.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="item-row">
                      <div className="form-group flex-2">
                        <label htmlFor={`product_${orderIndex}_${itemIndex}`}>Product *</label>
                        <select
                          id={`product_${orderIndex}_${itemIndex}`}
                          value={item.productId}
                          onChange={(e) => handleItemChange(orderIndex, itemIndex, 'productId', e.target.value)}
                          className={errors[`order_${orderIndex}_item_${itemIndex}_product`] ? 'error' : ''}
                          required
                        >
                          <option value="">Select a product...</option>
                          {products.map(product => (
                            <option key={product.id} value={product.id}>
                              {product.name} (Stock: {product.quantity})
                            </option>
                          ))}
                        </select>
                        {errors[`order_${orderIndex}_item_${itemIndex}_product`] && (
                          <span className="error-text">{errors[`order_${orderIndex}_item_${itemIndex}_product`]}</span>
                        )}
                      </div>

                      <div className="form-group">
                        <label htmlFor={`quantity_${orderIndex}_${itemIndex}`}>Quantity *</label>
                        <input
                          type="number"
                          id={`quantity_${orderIndex}_${itemIndex}`}
                          value={item.quantity}
                          onChange={(e) => handleItemChange(orderIndex, itemIndex, 'quantity', e.target.value)}
                          className={errors[`order_${orderIndex}_item_${itemIndex}_quantity`] ? 'error' : ''}
                          min="1"
                          required
                        />
                        {errors[`order_${orderIndex}_item_${itemIndex}_quantity`] && (
                          <span className="error-text">{errors[`order_${orderIndex}_item_${itemIndex}_quantity`]}</span>
                        )}
                      </div>

                      {order.items.length > 1 && (
                        <button
                          type="button"
                          className="remove-item-btn"
                          onClick={() => removeItem(orderIndex, itemIndex)}
                          title="Remove item"
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                {/* Order-specific reason and notes */}
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor={`reason_${orderIndex}`}>Return Reason</label>
                    <select
                      id={`reason_${orderIndex}`}
                      value={order.reason}
                      onChange={(e) => handleOrderChange(orderIndex, 'reason', e.target.value)}
                    >
                      <option value="">Select reason...</option>
                      <option value="defective">Defective Product</option>
                      <option value="wrong_item">Wrong Item Sent</option>
                      <option value="damaged">Damaged in Transit</option>
                      <option value="customer_change">Customer Changed Mind</option>
                      <option value="quality_issue">Quality Issue</option>
                      <option value="expired">Expired Product</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label htmlFor={`notes_${orderIndex}`}>Notes</label>
                    <textarea
                      id={`notes_${orderIndex}`}
                      value={order.notes}
                      onChange={(e) => handleOrderChange(orderIndex, 'notes', e.target.value)}
                      placeholder="Notes for this order..."
                      rows="2"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Global Status */}
          <div className="form-group">
            <label htmlFor="status">Overall Status</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
            >
              <option value="received">✅ Received</option>
              <option value="pending">⏳ Pending</option>
              <option value="processed">📋 Processed</option>
            </select>
          </div>

          {/* Submit Buttons */}
          <div className="form-actions">
            {errors.submit && <div className="error-text">{errors.submit}</div>}
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : (editingReturn ? 'Update Return' : `Create ${formData.orders.length} Return${formData.orders.length > 1 ? 's' : ''}`)}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReturnForm;
