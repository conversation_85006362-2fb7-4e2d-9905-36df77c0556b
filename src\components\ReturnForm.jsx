import React, { useState, useEffect } from 'react';
import { useProducts } from '../hooks/useProducts.js';
import returnService from '../services/returnService.js';
import './ReturnForm.css';

const ReturnForm = ({ isOpen, onClose, onReturnCreated, editingReturn = null }) => {
  const { products } = useProducts();

  const [formData, setFormData] = useState({
    orderId: '',
    date: new Date().toISOString().split('T')[0],
    items: [{ productId: '', productName: '', quantity: 1 }],
    reason: '',
    status: 'received',
    notes: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (editingReturn) {
        setFormData({
          orderId: editingReturn.orderId,
          date: editingReturn.date,
          items: editingReturn.items || [{ productId: '', productName: '', quantity: 1 }],
          reason: editingReturn.reason || '',
          status: editingReturn.status || 'received',
          notes: editingReturn.notes || ''
        });
      } else {
        setFormData({
          orderId: '',
          date: new Date().toISOString().split('T')[0],
          items: [{ productId: '', productName: '', quantity: 1 }],
          reason: '',
          status: 'received',
          notes: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, editingReturn]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      [field]: field === 'quantity' ? parseInt(value) || 0 : value
    };

    // Add product name for display
    if (field === 'productId' && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        newItems[index].productName = product.name;
      }
    }

    setFormData(prev => ({
      ...prev,
      items: newItems
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { productId: '', productName: '', quantity: 1 }]
    }));
  };

  const removeItem = (index) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index)
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.orderId.trim()) {
      newErrors.orderId = 'Order ID is required';
    }

    if (!formData.date) {
      newErrors.date = 'Return date is required';
    }

    // Validate items
    formData.items.forEach((item, index) => {
      if (!item.productId) {
        newErrors[`item_${index}_product`] = 'Product is required';
      }
      if (!item.quantity || item.quantity <= 0) {
        newErrors[`item_${index}_quantity`] = 'Valid quantity is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare items with product names
      const itemsWithNames = formData.items.map(item => {
        const product = products.find(p => p.id === item.productId);
        return {
          ...item,
          productName: product ? product.name : 'Unknown Product'
        };
      });

      const returnData = {
        ...formData,
        items: itemsWithNames
      };

      let result;
      if (editingReturn) {
        result = returnService.updateReturn(editingReturn.id, returnData);
      } else {
        result = returnService.createReturn(returnData);
      }

      onReturnCreated(result);
      onClose();
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content return-form-modal">
        <div className="modal-header">
          <h2>{editingReturn ? '📝 Edit Return' : '↩️ New Return'}</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit} className="return-form" style={{ padding: '1.5rem' }}>
          {/* Order ID and Date */}
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="orderId">Order ID *</label>
              <input
                type="text"
                id="orderId"
                name="orderId"
                value={formData.orderId}
                onChange={handleInputChange}
                className={errors.orderId ? 'error' : ''}
                placeholder="Enter order ID..."
                required
              />
              {errors.orderId && <span className="error-text">{errors.orderId}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="date">Return Date *</label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className={errors.date ? 'error' : ''}
                required
              />
              {errors.date && <span className="error-text">{errors.date}</span>}
            </div>
          </div>

          {/* Items Section */}
          <div className="form-section">
            <div className="section-header">
              <h3>📦 Items to Return</h3>
              <button type="button" className="btn btn-primary" onClick={addItem}>
                + Add Item
              </button>
            </div>

            {formData.items.map((item, index) => (
              <div key={index} className="item-row">
                <div className="form-group flex-2">
                  <label htmlFor={`product_${index}`}>Product *</label>
                  <select
                    id={`product_${index}`}
                    value={item.productId}
                    onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                    className={errors[`item_${index}_product`] ? 'error' : ''}
                    required
                  >
                    <option value="">Select a product...</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.name} (Stock: {product.quantity})
                      </option>
                    ))}
                  </select>
                  {errors[`item_${index}_product`] && (
                    <span className="error-text">{errors[`item_${index}_product`]}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor={`quantity_${index}`}>Quantity *</label>
                  <input
                    type="number"
                    id={`quantity_${index}`}
                    value={item.quantity}
                    onChange={(e) => handleItemChange(index, 'quantity', e.target.value)}
                    className={errors[`item_${index}_quantity`] ? 'error' : ''}
                    min="1"
                    required
                  />
                  {errors[`item_${index}_quantity`] && (
                    <span className="error-text">{errors[`item_${index}_quantity`]}</span>
                  )}
                </div>

                {formData.items.length > 1 && (
                  <button
                    type="button"
                    className="remove-item-btn"
                    onClick={() => removeItem(index)}
                    title="Remove item"
                  >
                    🗑️
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Reason and Status */}
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="reason">Return Reason</label>
              <select
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleInputChange}
              >
                <option value="">Select reason...</option>
                <option value="defective">Defective Product</option>
                <option value="wrong_item">Wrong Item Sent</option>
                <option value="damaged">Damaged in Transit</option>
                <option value="customer_change">Customer Changed Mind</option>
                <option value="quality_issue">Quality Issue</option>
                <option value="expired">Expired Product</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
              >
                <option value="received">✅ Received</option>
                <option value="pending">⏳ Pending</option>
                <option value="processed">📋 Processed</option>
              </select>
            </div>
          </div>

          {/* Notes */}
          <div className="form-group">
            <label htmlFor="notes">Notes</label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              placeholder="Additional notes about this return..."
              rows="3"
            />
          </div>

          {/* Submit Buttons */}
          <div className="form-actions">
            {errors.submit && <div className="error-text">{errors.submit}</div>}
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : (editingReturn ? 'Update Return' : 'Create Return')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReturnForm;
