import { useState, useEffect, useCallback } from 'react';
import authService, { USER_ROLES } from '../services/authService.js';

// Custom hook for authentication management
export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load current user on mount
  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
    setLoading(false);
  }, []);

  // Listen for auth events
  useEffect(() => {
    const handleUserLoggedIn = (event) => {
      setUser(event.detail.user);
      setError(null);
    };

    const handleUserLoggedOut = () => {
      setUser(null);
      setError(null);
    };

    window.addEventListener('userLoggedIn', handleUserLoggedIn);
    window.addEventListener('userLoggedOut', handleUserLoggedOut);

    return () => {
      window.removeEventListener('userLoggedIn', handleUserLoggedIn);
      window.removeEventListener('userLoggedOut', handleUserLoggedOut);
    };
  }, []);

  // Login function
  const login = useCallback(async (username, password) => {
    setLoading(true);
    setError(null);
    try {
      const loggedInUser = authService.login(username, password);
      setUser(loggedInUser);
      return loggedInUser;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Logout function
  const logout = useCallback(() => {
    setLoading(true);
    try {
      authService.logout();
      setUser(null);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, []);

  // Check permissions
  const hasPermission = useCallback((permission) => {
    return authService.hasPermission(permission);
  }, []);

  // Check if user is authenticated
  const isAuthenticated = useCallback(() => {
    return authService.isAuthenticated();
  }, []);

  // Check if user is admin
  const isAdmin = useCallback(() => {
    return authService.isAdmin();
  }, []);

  // Check if user is viewer
  const isViewer = useCallback(() => {
    return authService.isViewer();
  }, []);

  return {
    user,
    loading,
    error,
    login,
    logout,
    hasPermission,
    isAuthenticated,
    isAdmin,
    isViewer,
    setError
  };
};

// Hook for user management (admin only)
export const useUserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load all users
  const loadUsers = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const allUsers = authService.getAllUsers();
      setUsers(allUsers);
    } catch (err) {
      setError('Failed to load users');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add new user
  const addUser = useCallback(async (userData) => {
    setLoading(true);
    setError(null);
    try {
      const newUser = authService.addUser(userData);
      setUsers(prev => [...prev, newUser]);
      return newUser;
    } catch (err) {
      setError(err.message);
      console.error('Error adding user:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update user
  const updateUser = useCallback(async (userId, updates) => {
    setLoading(true);
    setError(null);
    try {
      const updatedUser = authService.updateUser(userId, updates);
      setUsers(prev => 
        prev.map(user => 
          user.id === userId ? updatedUser : user
        )
      );
      return updatedUser;
    } catch (err) {
      setError(err.message);
      console.error('Error updating user:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete user
  const deleteUser = useCallback(async (userId) => {
    setLoading(true);
    setError(null);
    try {
      await authService.deleteUser(userId);
      setUsers(prev => prev.filter(user => user.id !== userId));
      return true;
    } catch (err) {
      setError(err.message);
      console.error('Error deleting user:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Load users on mount
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  return {
    users,
    loading,
    error,
    addUser,
    updateUser,
    deleteUser,
    refreshUsers: loadUsers,
    setError
  };
};

// Permission wrapper component
export const PermissionWrapper = ({ permission, children, fallback = null }) => {
  const { hasPermission } = useAuth();
  
  if (hasPermission(permission)) {
    return children;
  }
  
  return fallback;
};

// Role wrapper component
export const RoleWrapper = ({ roles, children, fallback = null }) => {
  const { user } = useAuth();
  
  if (user && roles.includes(user.role)) {
    return children;
  }
  
  return fallback;
};

export default useAuth;
