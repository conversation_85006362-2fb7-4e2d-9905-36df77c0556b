import { useState, useEffect, useCallback } from 'react';
import databaseService from '../services/databaseService.js';

// Custom hook for managing products with CRUD operations
export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load all products
  const loadProducts = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const allProducts = databaseService.getAllProducts();
      setProducts(allProducts);
    } catch (err) {
      setError('Failed to load products');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add new product
  const addProduct = useCallback(async (productData) => {
    setLoading(true);
    setError(null);
    try {
      const newProduct = databaseService.addProduct(productData);
      setProducts(prev => [...prev, newProduct]);
      return newProduct;
    } catch (err) {
      setError('Failed to add product');
      console.error('Error adding product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update existing product
  const updateProduct = useCallback(async (id, updates) => {
    setLoading(true);
    setError(null);
    try {
      const updatedProduct = databaseService.updateProduct(id, updates);
      setProducts(prev => 
        prev.map(product => 
          product.id === id ? updatedProduct : product
        )
      );
      return updatedProduct;
    } catch (err) {
      setError('Failed to update product');
      console.error('Error updating product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete product
  const deleteProduct = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    try {
      const deletedProduct = databaseService.deleteProduct(id);
      setProducts(prev => prev.filter(product => product.id !== id));
      return deletedProduct;
    } catch (err) {
      setError('Failed to delete product');
      console.error('Error deleting product:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Search products
  const searchProducts = useCallback(async (query) => {
    setLoading(true);
    setError(null);
    try {
      const results = databaseService.searchProducts(query);
      return results;
    } catch (err) {
      setError('Failed to search products');
      console.error('Error searching products:', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Get product by ID
  const getProduct = useCallback((id) => {
    return databaseService.getProductById(id);
  }, []);

  // Load products on mount
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  return {
    products,
    loading,
    error,
    addProduct,
    updateProduct,
    deleteProduct,
    searchProducts,
    getProduct,
    refreshProducts: loadProducts
  };
};

// Hook for managing product categories
export const useCategories = () => {
  const [categories, setCategories] = useState([]);

  const loadCategories = useCallback(() => {
    const products = databaseService.getAllProducts();
    const uniqueCategories = [...new Set(products.map(p => p.category))];
    setCategories(['All', ...uniqueCategories.sort()]);
  }, []);

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  return {
    categories,
    refreshCategories: loadCategories
  };
};
