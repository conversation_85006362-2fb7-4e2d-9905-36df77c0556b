/* Purchase List */
.purchase-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 2px solid #e9ecef;
}

.list-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.empty-state p {
  margin: 0;
  font-size: 1.1rem;
}

/* Purchase Cards */
.purchase-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.purchase-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.purchase-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Purchase Header */
.purchase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.purchase-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-label {
  font-size: 1.2rem;
}

.date-value {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.purchase-actions {
  display: flex;
  gap: 0.5rem;
}

/* Purchase Summary */
.purchase-summary {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-item.total {
  margin-left: auto;
}

.summary-label {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.summary-item.total .summary-value {
  color: #007bff;
  font-size: 1.4rem;
}

/* Purchase Items */
.purchase-items {
  margin-bottom: 1rem;
}

.purchase-items h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.items-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.item-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.item-details {
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 0.9rem;
}

.item-quantity {
  background: #007bff;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.item-price {
  color: #666;
  font-weight: 500;
}

.item-total {
  font-weight: 600;
  color: #28a745;
  min-width: 80px;
  text-align: right;
}

/* Purchase Notes */
.purchase-notes {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #fff3cd;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
}

.purchase-notes h4 {
  margin: 0 0 0.5rem 0;
  color: #856404;
  font-size: 0.9rem;
}

.purchase-notes p {
  margin: 0;
  color: #856404;
  font-style: italic;
}

/* Purchase Meta */
.purchase-meta {
  display: flex;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
  font-size: 0.8rem;
  color: #666;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Buttons */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .purchase-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .purchase-actions {
    justify-content: center;
  }

  .purchase-summary {
    flex-direction: column;
    gap: 1rem;
  }

  .summary-item.total {
    margin-left: 0;
  }

  .item-row {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .item-details {
    justify-content: space-between;
  }

  .purchase-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .purchase-card {
    padding: 1rem;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .item-details {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .item-total {
    text-align: left;
  }
}

/* Animation */
.purchase-card {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
