import databaseService from '../services/databaseService.js';
import { initialProducts } from '../data/initialProducts.js';

// Initialize database with initial product data
export const initializeDatabase = () => {
  const existingProducts = databaseService.getAllProducts();
  
  // Only initialize if database is empty
  if (existingProducts.length === 0) {
    console.log('Initializing database with initial products...');
    
    initialProducts.forEach(product => {
      try {
        databaseService.addProduct(product);
      } catch (error) {
        console.error(`Error adding product ${product.name}:`, error);
      }
    });
    
    console.log(`Database initialized with ${initialProducts.length} products`);
    return true;
  } else {
    console.log(`Database already contains ${existingProducts.length} products`);
    return false;
  }
};

// Reset database to initial state
export const resetDatabase = () => {
  // Clear existing data
  localStorage.removeItem('inventory_products');
  
  // Reinitialize
  databaseService.initializeDatabase();
  initializeDatabase();
  
  console.log('Database reset to initial state');
};

// Get database statistics
export const getDatabaseStats = () => {
  const products = databaseService.getAllProducts();
  const metadata = databaseService.getMetadata();
  
  const stats = {
    totalProducts: products.length,
    categoryCounts: {},
    lastUpdated: metadata?.lastUpdated,
    lowStockItems: products.filter(p => p.quantity < 5).length,
    outOfStockItems: products.filter(p => p.quantity === 0).length
  };
  
  // Count products by category
  products.forEach(product => {
    const category = product.category || 'Uncategorized';
    stats.categoryCounts[category] = (stats.categoryCounts[category] || 0) + 1;
  });
  
  return stats;
};
