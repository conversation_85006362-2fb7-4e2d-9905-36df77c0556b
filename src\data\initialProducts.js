// Initial product list to populate the database
export const initialProducts = [
  // Skincare Products
  { name: "Survana Rosemary", category: "Skincare", quantity: 0 },
  { name: "O. HS (New)", category: "Skincare", quantity: 0 },
  { name: "O. Argireline Solution", category: "Skincare", quantity: 0 },
  { name: "O. Ascorbic Acid", category: "Skincare", quantity: 0 },
  { name: "O. Ascorbyl Glucoside Solution", category: "Skincare", quantity: 0 },
  { name: "O. Niacinamide", category: "Skincare", quantity: 0 },
  { name: "O. Retinol 1%", category: "Skincare", quantity: 0 },
  { name: "O. Old Hair Serum", category: "Hair Care", quantity: 0 },
  { name: "O. GA 240 ml", category: "Skincare", quantity: 0 },
  { name: "O. GA 100 ml", category: "Skincare", quantity: 0 },
  { name: "O. SA 2%", category: "Skincare", quantity: 0 },
  { name: "O. Alpha Arbutin 2%", category: "Skincare", quantity: 0 },
  { name: "O. Eye Serum", category: "Skincare", quantity: 0 },
  { name: "O. Caffine Solution", category: "Skincare", quantity: 0 },
  { name: "O. Sunscreen SPF 30", category: "Sunscreen", quantity: 0 },
  { name: "LR-Mela B3", category: "Skincare", quantity: 0 },
  { name: "LR-FS", category: "Skincare", quantity: 0 },
  { name: "LR-RT", category: "Skincare", quantity: 0 },
  { name: "LR-C", category: "Skincare", quantity: 0 },
  { name: "Anua Niacinamide", category: "Skincare", quantity: 0 },
  { name: "Anua Niacinamide New", category: "Skincare", quantity: 0 },
  { name: "Anua Heartleaf Cleansing Oil", category: "Cleanser", quantity: 0 },
  { name: "Dr. Althea 345 Relief Cream", category: "Skincare", quantity: 0 },
  { name: "Bioderma Facewash 200ml", category: "Cleanser", quantity: 0 },
  { name: "I'm From Rice Toner", category: "Toner", quantity: 0 },
  { name: "I'm From Rice Cream", category: "Moisturizer", quantity: 0 },
  { name: "ROC Glow Serum", category: "Serum", quantity: 0 },
  { name: "Paula's Choice 118 ml", category: "Skincare", quantity: 0 },
  { name: "Mielle Rosemary Oil", category: "Hair Care", quantity: 0 },
  { name: "Beauty of Joseon Rice Sunscreen", category: "Sunscreen", quantity: 0 },
  { name: "Beauty of Joseon Glow Serum", category: "Serum", quantity: 0 },
  { name: "Beauty of Joseon Sun Stick", category: "Sunscreen", quantity: 0 },
  { name: "Some BY MI Vitamin C", category: "Serum", quantity: 0 },
  { name: "Glutathione", category: "Supplement", quantity: 0 },
  
  // CeraVe Products
  { name: "Cerave Night Cream", category: "Moisturizer", quantity: 0 },
  { name: "Cerave Acnee Control Cleanser", category: "Cleanser", quantity: 0 },
  { name: "Cerave Moisturizer", category: "Moisturizer", quantity: 0 },
  { name: "Cerave Foaming Cleanser", category: "Cleanser", quantity: 0 },
  { name: "Cerave Resurfacing Retinol", category: "Retinol", quantity: 0 },
  { name: "Cerave Skin Renewing Retinol", category: "Retinol", quantity: 0 },
  { name: "Cerave Skin Renewing Nightly Treatment", category: "Treatment", quantity: 0 },
  
  // COSRX Products
  { name: "COSRX Gentle Cleanser (Red)", category: "Cleanser", quantity: 0 },
  { name: "COSRX Aloe Sunscreen", category: "Sunscreen", quantity: 0 },
  
  // Other Skincare
  { name: "Christian Sunscreen 70ml", category: "Sunscreen", quantity: 0 },
  { name: "Cotton Pad", category: "Accessories", quantity: 0 },
  { name: "SKIN1004 Ampoule", category: "Ampoule", quantity: 0 },
  { name: "MISSHA Sun Milk", category: "Sunscreen", quantity: 0 },
  { name: "EltaMD Sunsceeen SPF 46", category: "Sunscreen", quantity: 0 },
  { name: "O. HS 30ml", category: "Hair Care", quantity: 0 },
  
  // Fragrances
  { name: "Mon Paris", category: "Fragrance", quantity: 0 },
  { name: "Vampire Blood", category: "Fragrance", quantity: 0 },
  { name: "Tam Dao", category: "Fragrance", quantity: 0 },
  { name: "Gucci OUD", category: "Fragrance", quantity: 0 },
  { name: "Tom Ford Tobacco", category: "Fragrance", quantity: 0 },
  { name: "Mont Blanc", category: "Fragrance", quantity: 0 },
  { name: "Hugo Boss", category: "Fragrance", quantity: 0 },
  { name: "Giorgio Armani", category: "Fragrance", quantity: 0 },
  { name: "Paco Rabanne", category: "Fragrance", quantity: 0 },
  { name: "Dunhill Desire", category: "Fragrance", quantity: 0 },
  { name: "Versace Eros", category: "Fragrance", quantity: 0 },
  { name: "Tommy Girl", category: "Fragrance", quantity: 0 },
  { name: "Dior Sauvage", category: "Fragrance", quantity: 0 },
  { name: "Nautica Voyage", category: "Fragrance", quantity: 0 },
  { name: "Gucci flora Gorgeous gardenia", category: "Fragrance", quantity: 0 },
  { name: "Good Girl by carolina herrera", category: "Fragrance", quantity: 0 },
  { name: "Davidoff cool water", category: "Fragrance", quantity: 0 },
  { name: "Dolce & gabbana the one", category: "Fragrance", quantity: 0 },
  { name: "creed aventus", category: "Fragrance", quantity: 0 },
  { name: "one million", category: "Fragrance", quantity: 0 },
  { name: "BLVGARI Men in Black", category: "Fragrance", quantity: 0 },
  { name: "Armani Code", category: "Fragrance", quantity: 0 },
  { name: "Blue de channel paris", category: "Fragrance", quantity: 0 }
];

// Product categories for filtering
export const productCategories = [
  "All",
  "Skincare",
  "Hair Care",
  "Sunscreen",
  "Cleanser",
  "Toner",
  "Moisturizer",
  "Serum",
  "Retinol",
  "Treatment",
  "Ampoule",
  "Accessories",
  "Supplement",
  "Fragrance"
];
