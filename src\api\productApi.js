import databaseService from '../services/databaseService.js';

// Product API functions for CRUD operations
export const productApi = {
  // Get all products
  async getAll() {
    try {
      return databaseService.getAllProducts();
    } catch (error) {
      console.error('API Error - Get All Products:', error);
      throw new Error('Failed to fetch products');
    }
  },

  // Get product by ID
  async getById(id) {
    try {
      const product = databaseService.getProductById(id);
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    } catch (error) {
      console.error('API Error - Get Product by ID:', error);
      throw error;
    }
  },

  // Create new product
  async create(productData) {
    try {
      // Validate required fields
      if (!productData.name || productData.name.trim() === '') {
        throw new Error('Product name is required');
      }

      const newProduct = databaseService.addProduct({
        name: productData.name.trim(),
        category: productData.category || 'General',
        quantity: parseInt(productData.quantity) || 0,
        description: productData.description || '',
        sku: productData.sku || undefined
      });

      return newProduct;
    } catch (error) {
      console.error('API Error - Create Product:', error);
      throw error;
    }
  },

  // Update existing product
  async update(id, updates) {
    try {
      // Validate ID
      if (!id) {
        throw new Error('Product ID is required');
      }

      // Prepare updates object
      const cleanUpdates = {};
      if (updates.name !== undefined) cleanUpdates.name = updates.name.trim();
      if (updates.category !== undefined) cleanUpdates.category = updates.category;
      if (updates.quantity !== undefined) cleanUpdates.quantity = parseInt(updates.quantity) || 0;
      if (updates.description !== undefined) cleanUpdates.description = updates.description;
      if (updates.sku !== undefined) cleanUpdates.sku = updates.sku;

      const updatedProduct = databaseService.updateProduct(id, cleanUpdates);
      return updatedProduct;
    } catch (error) {
      console.error('API Error - Update Product:', error);
      throw error;
    }
  },

  // Delete product
  async delete(id) {
    try {
      if (!id) {
        throw new Error('Product ID is required');
      }

      const deletedProduct = databaseService.deleteProduct(id);
      return deletedProduct;
    } catch (error) {
      console.error('API Error - Delete Product:', error);
      throw error;
    }
  },

  // Search products
  async search(query) {
    try {
      if (!query || query.trim() === '') {
        return this.getAll();
      }

      return databaseService.searchProducts(query.trim());
    } catch (error) {
      console.error('API Error - Search Products:', error);
      throw error;
    }
  },

  // Filter products by category
  async filterByCategory(category) {
    try {
      const allProducts = databaseService.getAllProducts();
      
      if (!category || category === 'All') {
        return allProducts;
      }

      return allProducts.filter(product => 
        product.category.toLowerCase() === category.toLowerCase()
      );
    } catch (error) {
      console.error('API Error - Filter by Category:', error);
      throw error;
    }
  },

  // Get low stock products
  async getLowStock(threshold = 5) {
    try {
      const allProducts = databaseService.getAllProducts();
      return allProducts.filter(product => product.quantity <= threshold);
    } catch (error) {
      console.error('API Error - Get Low Stock:', error);
      throw error;
    }
  },

  // Update product quantity
  async updateQuantity(id, quantity) {
    try {
      return this.update(id, { quantity: parseInt(quantity) || 0 });
    } catch (error) {
      console.error('API Error - Update Quantity:', error);
      throw error;
    }
  },

  // Bulk operations
  async bulkUpdate(updates) {
    try {
      const results = [];
      for (const update of updates) {
        const result = await this.update(update.id, update.data);
        results.push(result);
      }
      return results;
    } catch (error) {
      console.error('API Error - Bulk Update:', error);
      throw error;
    }
  },

  // Export data
  async exportData() {
    try {
      return databaseService.exportData();
    } catch (error) {
      console.error('API Error - Export Data:', error);
      throw error;
    }
  },

  // Import data
  async importData(jsonData) {
    try {
      return databaseService.importData(jsonData);
    } catch (error) {
      console.error('API Error - Import Data:', error);
      throw error;
    }
  }
};

export default productApi;
