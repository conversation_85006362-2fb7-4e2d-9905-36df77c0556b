import React from 'react';
import './ProductList.css';

const ProductList = ({ products, onEdit, onDelete, loading }) => {
  if (loading) {
    return <div className="loading">Loading products...</div>;
  }

  if (products.length === 0) {
    return (
      <div className="empty-state">
        <h3>No products found</h3>
        <p>Add your first product to get started!</p>
      </div>
    );
  }

  const getStockStatus = (quantity) => {
    if (quantity === 0) return 'out-of-stock';
    if (quantity <= 5) return 'low-stock';
    return 'in-stock';
  };

  const getStockLabel = (quantity) => {
    if (quantity === 0) return 'Out of Stock';
    if (quantity <= 5) return 'Low Stock';
    return 'In Stock';
  };

  return (
    <div className="product-list">
      <div className="product-grid">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <div className="product-header">
              <h3 className="product-name">{product.name}</h3>
              <span className="product-sku">SKU: {product.sku}</span>
            </div>
            
            <div className="product-details">
              <div className="product-category">
                <span className="category-badge">{product.category}</span>
              </div>
              
              <div className="product-quantity">
                <span className="quantity-label">Quantity:</span>
                <span className={`quantity-value ${getStockStatus(product.quantity)}`}>
                  {product.quantity}
                </span>
                <span className={`stock-status ${getStockStatus(product.quantity)}`}>
                  {getStockLabel(product.quantity)}
                </span>
              </div>

              {product.description && (
                <div className="product-description">
                  <p>{product.description}</p>
                </div>
              )}
            </div>

            <div className="product-meta">
              <small>Created: {new Date(product.createdAt).toLocaleDateString()}</small>
              {product.updatedAt !== product.createdAt && (
                <small>Updated: {new Date(product.updatedAt).toLocaleDateString()}</small>
              )}
            </div>

            <div className="product-actions">
              <button 
                className="btn btn-secondary"
                onClick={() => onEdit(product)}
              >
                ✏️ Edit
              </button>
              <button 
                className="btn btn-danger"
                onClick={() => onDelete(product.id)}
              >
                🗑️ Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="product-summary">
        <p>Showing {products.length} product{products.length !== 1 ? 's' : ''}</p>
      </div>
    </div>
  );
};

export default ProductList;
