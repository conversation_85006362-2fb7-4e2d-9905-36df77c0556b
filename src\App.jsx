import { useState, useEffect } from 'react';
import './App.css';
import ProductList from './components/ProductList';
import ProductForm from './components/ProductForm';
import SearchBar from './components/SearchBar';
import CategoryFilter from './components/CategoryFilter';
import Dashboard from './components/Dashboard';
import DeliveryPage from './components/DeliveryPage';
import ReturnPage from './components/ReturnPage';
import PurchasePage from './components/PurchasePage';
import LoginForm from './components/LoginForm';
import AdminPanel from './components/AdminPanel';
import { useProducts } from './hooks/useProducts';
import { useAuth, PermissionWrapper } from './hooks/useAuth';
import { initializeDatabase } from './utils/initializeData';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [editingProduct, setEditingProduct] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [showAdminPanel, setShowAdminPanel] = useState(false);

  // Authentication hook
  const { isAuthenticated, user, hasPermission } = useAuth();

  const {
    products,
    loading,
    error,
    addProduct,
    updateProduct,
    deleteProduct,
    searchProducts,
    refreshProducts
  } = useProducts();

  // Initialize database on first load
  useEffect(() => {
    initializeDatabase();
  }, []);

  // Listen for inventory updates from delivery and return systems
  useEffect(() => {
    const handleInventoryUpdate = () => {
      refreshProducts();
    };

    window.addEventListener('inventoryUpdated', handleInventoryUpdate);
    return () => {
      window.removeEventListener('inventoryUpdated', handleInventoryUpdate);
    };
  }, [refreshProducts]);

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleAddProduct = async (productData) => {
    try {
      await addProduct(productData);
      setShowForm(false);
    } catch (error) {
      console.error('Failed to add product:', error);
    }
  };

  const handleUpdateProduct = async (productData) => {
    try {
      await updateProduct(editingProduct.id, productData);
      setEditingProduct(null);
      setShowForm(false);
    } catch (error) {
      console.error('Failed to update product:', error);
    }
  };

  const handleDeleteProduct = async (id) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await deleteProduct(id);
      } catch (error) {
        console.error('Failed to delete product:', error);
      }
    }
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setEditingProduct(null);
    setShowForm(false);
  };

  // Show login form if not authenticated
  if (!isAuthenticated()) {
    return <LoginForm onLoginSuccess={() => {}} />;
  }

  // Show admin panel if requested
  if (showAdminPanel) {
    return <AdminPanel onClose={() => setShowAdminPanel(false)} />;
  }

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <h1>📦 Inventory Management System</h1>
          <div className="header-actions">
            <div className="user-info">
              <span className="user-name">👤 {user?.name}</span>
              <span className="user-role">({user?.role})</span>
            </div>
            <PermissionWrapper permission="canAccessAdmin">
              <button
                className="admin-button"
                onClick={() => setShowAdminPanel(true)}
                title="Admin Panel"
              >
                🛡️ Admin
              </button>
            </PermissionWrapper>
          </div>
        </div>
        <nav className="nav-tabs">
          <button
            className={activeTab === 'dashboard' ? 'active' : ''}
            onClick={() => setActiveTab('dashboard')}
          >
            📊 Dashboard
          </button>
          <button
            className={activeTab === 'products' ? 'active' : ''}
            onClick={() => setActiveTab('products')}
          >
            📦 Products
          </button>
          <button
            className={activeTab === 'deliveries' ? 'active' : ''}
            onClick={() => setActiveTab('deliveries')}
          >
            🚚 Deliveries
          </button>
          <button
            className={activeTab === 'returns' ? 'active' : ''}
            onClick={() => setActiveTab('returns')}
          >
            ↩️ Returns
          </button>
          <button
            className={activeTab === 'purchases' ? 'active' : ''}
            onClick={() => setActiveTab('purchases')}
          >
            🛒 Purchases
          </button>
        </nav>
      </header>

      <main className="app-main">
        {error && <div className="error-message">{error}</div>}

        {activeTab === 'dashboard' && (
          <Dashboard products={products} />
        )}

        {activeTab === 'products' && (
          <>
            <div className="toolbar">
              <SearchBar
                value={searchQuery}
                onChange={setSearchQuery}
                placeholder="Search products..."
              />
              <CategoryFilter
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
              />
              <PermissionWrapper permission="canCreate">
                <button
                  className="btn btn-primary"
                  onClick={() => setShowForm(true)}
                >
                  + Add Product
                </button>
              </PermissionWrapper>
            </div>

            {showForm && (
              <PermissionWrapper permission="canCreate">
                <ProductForm
                  product={editingProduct}
                  onSubmit={editingProduct ? handleUpdateProduct : handleAddProduct}
                  onCancel={handleCancelForm}
                  loading={loading}
                />
              </PermissionWrapper>
            )}

            <ProductList
              products={filteredProducts}
              onEdit={hasPermission('canEdit') ? handleEditProduct : null}
              onDelete={hasPermission('canDelete') ? handleDeleteProduct : null}
              loading={loading}
            />
          </>
        )}

        {activeTab === 'deliveries' && (
          <DeliveryPage />
        )}

        {activeTab === 'returns' && (
          <ReturnPage />
        )}

        {activeTab === 'purchases' && (
          <PurchasePage />
        )}
      </main>
    </div>
  );
}

export default App;
