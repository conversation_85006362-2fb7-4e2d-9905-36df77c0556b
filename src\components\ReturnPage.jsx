import React, { useState, useEffect } from 'react';
import ReturnForm from './ReturnForm.jsx';
import ReturnList from './ReturnList.jsx';
import returnService from '../services/returnService.js';
import './ReturnPage.css';

const ReturnPage = () => {
  const [returns, setReturns] = useState([]);
  const [filteredReturns, setFilteredReturns] = useState([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingReturn, setEditingReturn] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [reasonFilter, setReasonFilter] = useState('');
  const [activeView, setActiveView] = useState('list');
  const [loading, setLoading] = useState(false);

  // Load returns on component mount
  useEffect(() => {
    loadReturns();
  }, []);

  // Filter returns when search/filter criteria change
  useEffect(() => {
    filterReturns();
  }, [returns, searchQuery, statusFilter, dateFilter, reasonFilter]);

  const loadReturns = async () => {
    setLoading(true);
    try {
      const allReturns = returnService.getAllReturns();
      setReturns(allReturns);
    } catch (error) {
      console.error('Error loading returns:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterReturns = () => {
    let filtered = [...returns];

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(returnItem =>
        returnItem.orderId.toLowerCase().includes(query) ||
        returnItem.reason?.toLowerCase().includes(query) ||
        returnItem.notes?.toLowerCase().includes(query) ||
        returnItem.items.some(item => 
          item.productName?.toLowerCase().includes(query)
        )
      );
    }

    // Status filter
    if (statusFilter) {
      filtered = filtered.filter(returnItem => returnItem.status === statusFilter);
    }

    // Date filter
    if (dateFilter) {
      const today = new Date();
      const filterDate = new Date(today);
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(returnItem => 
            new Date(returnItem.date) >= filterDate
          );
          break;
        case 'week':
          filterDate.setDate(today.getDate() - 7);
          filtered = filtered.filter(returnItem => 
            new Date(returnItem.date) >= filterDate
          );
          break;
        case 'month':
          filterDate.setMonth(today.getMonth() - 1);
          filtered = filtered.filter(returnItem => 
            new Date(returnItem.date) >= filterDate
          );
          break;
      }
    }

    // Reason filter
    if (reasonFilter) {
      filtered = filtered.filter(returnItem => returnItem.reason === reasonFilter);
    }

    setFilteredReturns(filtered);
  };

  const handleCreateReturn = () => {
    setEditingReturn(null);
    setIsFormOpen(true);
  };

  const handleEditReturn = (returnItem) => {
    setEditingReturn(returnItem);
    setIsFormOpen(true);
  };

  const handleDeleteReturn = async (returnId) => {
    try {
      await returnService.deleteReturn(returnId);
      loadReturns();
    } catch (error) {
      console.error('Error deleting return:', error);
      alert('Failed to delete return: ' + error.message);
    }
  };

  const handleReturnCreated = (newReturns) => {
    // newReturns can be a single return or an array of returns
    loadReturns();

    // Show success message
    const count = Array.isArray(newReturns) ? newReturns.length : 1;
    console.log(`Successfully created ${count} return${count > 1 ? 's' : ''}`);
  };

  const handleRefresh = () => {
    loadReturns();
  };

  const clearFilters = () => {
    setSearchQuery('');
    setStatusFilter('');
    setDateFilter('');
    setReasonFilter('');
  };

  const getStats = () => {
    const stats = returnService.getReturnStats();
    return {
      total: stats.totalReturns,
      totalItems: stats.totalItemsReturned,
      received: stats.returnsByStatus.received || 0,
      pending: stats.returnsByStatus.pending || 0,
      processed: stats.returnsByStatus.processed || 0
    };
  };

  // Calculate total return quantities for each product
  const getReturnQuantities = () => {
    const quantities = {};

    returns.forEach(returnItem => {
      returnItem.items.forEach(item => {
        if (quantities[item.productId]) {
          quantities[item.productId].quantity += item.quantity;
          quantities[item.productId].returnIds.add(returnItem.orderId);
        } else {
          quantities[item.productId] = {
            productName: item.productName,
            quantity: item.quantity,
            returnIds: new Set([returnItem.orderId])
          };
        }
      });
    });

    return Object.entries(quantities)
      .map(([productId, data]) => ({
        productId,
        productName: data.productName,
        totalReturned: data.quantity,
        returnIds: Array.from(data.returnIds)
      }))
      .sort((a, b) => b.totalReturned - a.totalReturned);
  };

  // Get all unique return order IDs
  const getReturnOrderIds = () => {
    const orderIds = new Set();
    returns.forEach(returnItem => {
      orderIds.add(returnItem.orderId);
    });
    return Array.from(orderIds).sort();
  };

  const stats = getStats();

  return (
    <div className="return-page">
      {/* Header */}
      <div className="return-header">
        <div className="header-content">
          <h1>↩️ Return Management</h1>
          <p>Track and manage product returns</p>
        </div>
        <button 
          className="btn btn-primary"
          onClick={handleCreateReturn}
        >
          ↩️ New Return
        </button>
      </div>

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <div className="stat-number">{stats.total}</div>
            <div className="stat-label">Total Returns</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">📦</div>
          <div className="stat-content">
            <div className="stat-number">{stats.totalItems}</div>
            <div className="stat-label">Items Returned</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <div className="stat-number">{stats.received}</div>
            <div className="stat-label">Received</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">⏳</div>
          <div className="stat-content">
            <div className="stat-number">{stats.pending}</div>
            <div className="stat-label">Pending</div>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="return-toolbar">
        <div className="toolbar-left">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search returns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <span className="search-icon">🔍</span>
          </div>
        </div>

        <div className="toolbar-right">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Statuses</option>
            <option value="received">✅ Received</option>
            <option value="pending">⏳ Pending</option>
            <option value="processed">📋 Processed</option>
          </select>

          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Dates</option>
            <option value="today">Today</option>
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
          </select>

          <select
            value={reasonFilter}
            onChange={(e) => setReasonFilter(e.target.value)}
            className="filter-select"
          >
            <option value="">All Reasons</option>
            <option value="defective">Defective Product</option>
            <option value="wrong_item">Wrong Item Sent</option>
            <option value="damaged">Damaged in Transit</option>
            <option value="customer_change">Customer Changed Mind</option>
            <option value="quality_issue">Quality Issue</option>
            <option value="expired">Expired Product</option>
            <option value="other">Other</option>
          </select>

          {(searchQuery || statusFilter || dateFilter || reasonFilter) && (
            <button
              className="btn btn-secondary"
              onClick={clearFilters}
              title="Clear all filters"
            >
              🧹 Clear
            </button>
          )}
        </div>
      </div>

      {/* Return Summaries */}
      {searchQuery === '' && statusFilter === '' && dateFilter === '' && reasonFilter === '' && (
        <div className="return-summaries">
          {/* Return Quantities Summary */}
          <div className="return-quantities">
            <h3>📦 Total Return Quantities</h3>
            <div className="quantities-grid">
              {getReturnQuantities().slice(0, 10).map(item => (
                <div key={item.productId} className="quantity-item">
                  <div className="quantity-name">{item.productName}</div>
                  <div className="quantity-amount">{item.totalReturned}</div>
                  <div className="quantity-orders">
                    {item.returnIds.length} order{item.returnIds.length > 1 ? 's' : ''}
                  </div>
                </div>
              ))}
              {getReturnQuantities().length === 0 && (
                <div className="no-data">No returned items yet</div>
              )}
            </div>
            {getReturnQuantities().length > 10 && (
              <div className="show-more">
                <small>Showing top 10 items. Total: {getReturnQuantities().length} products</small>
              </div>
            )}
          </div>

          {/* Return Order IDs Summary */}
          <div className="return-order-ids">
            <h3>📋 Return Order IDs ({getReturnOrderIds().length})</h3>
            <div className="order-ids-container">
              {getReturnOrderIds().length > 0 ? (
                <div className="order-ids-list">
                  {getReturnOrderIds().map(orderId => (
                    <span key={orderId} className="order-id-tag">
                      {orderId}
                    </span>
                  ))}
                </div>
              ) : (
                <div className="no-data">No return orders yet</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <ReturnList
        returns={filteredReturns}
        onEditReturn={handleEditReturn}
        onDeleteReturn={handleDeleteReturn}
        onRefresh={handleRefresh}
      />

      {/* Return Form Modal */}
      <ReturnForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onReturnCreated={handleReturnCreated}
        editingReturn={editingReturn}
      />

      {/* Quick Actions */}
      <div className="quick-actions">
        <button
          className="quick-action-btn"
          onClick={handleRefresh}
          title="Refresh returns"
        >
          🔄
        </button>
        <button
          className="quick-action-btn"
          onClick={clearFilters}
          title="Clear all filters"
        >
          🧹
        </button>
      </div>
    </div>
  );
};

export default ReturnPage;
