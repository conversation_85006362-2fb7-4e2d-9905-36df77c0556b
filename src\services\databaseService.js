// Database service for managing products
class DatabaseService {
  constructor() {
    this.storageKey = 'inventory_products';
    this.initializeDatabase();
  }

  // Initialize database with default structure
  initializeDatabase() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        products: [],
        metadata: {
          lastUpdated: new Date().toISOString(),
          version: "1.0.0",
          totalProducts: 0
        }
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  // Get all products
  getAllProducts() {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      return data.products || [];
    } catch (error) {
      console.error('Error reading products:', error);
      return [];
    }
  }

  // Get product by ID
  getProductById(id) {
    const products = this.getAllProducts();
    return products.find(product => product.id === id);
  }

  // Add new product
  addProduct(productData) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const newProduct = {
        id: this.generateId(),
        name: productData.name,
        sku: productData.sku || this.generateSKU(productData.name),
        quantity: productData.quantity || 0,
        category: productData.category || 'General',
        description: productData.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      data.products.push(newProduct);
      data.metadata.totalProducts = data.products.length;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return newProduct;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  }

  // Update product
  updateProduct(id, updates) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const productIndex = data.products.findIndex(product => product.id === id);
      
      if (productIndex === -1) {
        throw new Error('Product not found');
      }

      data.products[productIndex] = {
        ...data.products[productIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      data.metadata.lastUpdated = new Date().toISOString();
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      
      return data.products[productIndex];
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  // Delete product
  deleteProduct(id) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const productIndex = data.products.findIndex(product => product.id === id);
      
      if (productIndex === -1) {
        throw new Error('Product not found');
      }

      const deletedProduct = data.products.splice(productIndex, 1)[0];
      data.metadata.totalProducts = data.products.length;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return deletedProduct;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  // Search products
  searchProducts(query) {
    const products = this.getAllProducts();
    const lowercaseQuery = query.toLowerCase();
    
    return products.filter(product => 
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.sku.toLowerCase().includes(lowercaseQuery) ||
      product.category.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Generate unique ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Generate SKU from product name
  generateSKU(name) {
    return name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 8) + 
      Date.now().toString().slice(-4);
  }

  // Get database metadata
  getMetadata() {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      return data.metadata;
    } catch (error) {
      console.error('Error reading metadata:', error);
      return null;
    }
  }

  // Export data
  exportData() {
    return localStorage.getItem(this.storageKey);
  }

  // Import data
  importData(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }
}

export default new DatabaseService();
