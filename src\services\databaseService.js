// Database service for managing products
class DatabaseService {
  constructor() {
    this.storageKey = 'inventory_products';
    this.initializeDatabase();
  }

  // Initialize database with default structure
  initializeDatabase() {
    const existingData = localStorage.getItem(this.storageKey);
    if (!existingData) {
      const initialData = {
        products: [],
        metadata: {
          lastUpdated: new Date().toISOString(),
          version: "1.0.0",
          totalProducts: 0
        }
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  // Get all products
  getAllProducts() {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      return data.products || [];
    } catch (error) {
      console.error('Error reading products:', error);
      return [];
    }
  }

  // Get product by ID
  getProductById(id) {
    const products = this.getAllProducts();
    return products.find(product => product.id === id);
  }

  // Add new product
  addProduct(productData) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const newProduct = {
        id: productData.id || this.generateId(),
        name: productData.name,
        sku: productData.sku || this.generateSKU(productData.name),
        quantity: productData.quantity || 0,
        category: productData.category || 'General',
        description: productData.description || '',
        price: productData.price || 0,
        purchasePrice: productData.purchasePrice || 0,
        lastPurchaseDate: productData.lastPurchaseDate || null,
        totalPurchased: productData.totalPurchased || (productData.quantity || 0),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      data.products.push(newProduct);
      data.metadata.totalProducts = data.products.length;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return newProduct;
    } catch (error) {
      console.error('Error adding product:', error);
      throw error;
    }
  }

  // Update product
  updateProduct(id, updates) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const productIndex = data.products.findIndex(product => product.id === id);
      
      if (productIndex === -1) {
        throw new Error('Product not found');
      }

      data.products[productIndex] = {
        ...data.products[productIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      data.metadata.lastUpdated = new Date().toISOString();
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      
      return data.products[productIndex];
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  // Delete product
  deleteProduct(id) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const productIndex = data.products.findIndex(product => product.id === id);
      
      if (productIndex === -1) {
        throw new Error('Product not found');
      }

      const deletedProduct = data.products.splice(productIndex, 1)[0];
      data.metadata.totalProducts = data.products.length;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return deletedProduct;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  // Search products
  searchProducts(query) {
    const products = this.getAllProducts();
    const lowercaseQuery = query.toLowerCase();
    
    return products.filter(product => 
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.sku.toLowerCase().includes(lowercaseQuery) ||
      product.category.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Generate unique ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Generate SKU from product name
  generateSKU(name) {
    return name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 8) + 
      Date.now().toString().slice(-4);
  }

  // Get database metadata
  getMetadata() {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      return data.metadata;
    } catch (error) {
      console.error('Error reading metadata:', error);
      return null;
    }
  }

  // Export data
  exportData() {
    return localStorage.getItem(this.storageKey);
  }

  // Import data
  importData(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Get products by category
  getProductsByCategory(category) {
    const products = this.getAllProducts();
    return products.filter(product => product.category === category);
  }

  // Get low stock products
  getLowStockProducts(threshold = 10) {
    const products = this.getAllProducts();
    return products.filter(product => product.quantity <= threshold);
  }

  // Get inventory value based on purchase prices
  getInventoryValue() {
    const products = this.getAllProducts();
    let totalValue = 0;
    let totalRetailValue = 0;

    products.forEach(product => {
      if (product.purchasePrice && product.quantity) {
        totalValue += product.purchasePrice * product.quantity;
      }
      if (product.price && product.quantity) {
        totalRetailValue += product.price * product.quantity;
      }
    });

    return {
      purchaseValue: totalValue,
      retailValue: totalRetailValue,
      potentialProfit: totalRetailValue - totalValue
    };
  }

  // Update product from purchase
  updateProductFromPurchase(productId, purchaseData) {
    try {
      const data = JSON.parse(localStorage.getItem(this.storageKey));
      const productIndex = data.products.findIndex(product => product.id === productId);

      if (productIndex !== -1) {
        const product = data.products[productIndex];

        // Update quantity and purchase tracking
        data.products[productIndex] = {
          ...product,
          quantity: product.quantity + purchaseData.quantity,
          purchasePrice: purchaseData.unitPrice,
          lastPurchaseDate: purchaseData.date,
          totalPurchased: (product.totalPurchased || 0) + purchaseData.quantity,
          updatedAt: new Date().toISOString()
        };

        data.metadata.lastUpdated = new Date().toISOString();
        localStorage.setItem(this.storageKey, JSON.stringify(data));

        return data.products[productIndex];
      }

      return null;
    } catch (error) {
      console.error('Error updating product from purchase:', error);
      throw error;
    }
  }

  // Get product statistics
  getProductStats() {
    const products = this.getAllProducts();
    const totalProducts = products.length;
    const totalQuantity = products.reduce((sum, product) => sum + product.quantity, 0);
    const categories = [...new Set(products.map(product => product.category))];
    const lowStockCount = this.getLowStockProducts().length;
    const inventoryValue = this.getInventoryValue();

    return {
      totalProducts,
      totalQuantity,
      categories: categories.length,
      lowStockCount,
      inventoryValue: inventoryValue.purchaseValue,
      retailValue: inventoryValue.retailValue,
      potentialProfit: inventoryValue.potentialProfit,
      categoryBreakdown: categories.map(category => ({
        name: category,
        count: products.filter(p => p.category === category).length
      }))
    };
  }
}

export default new DatabaseService();
