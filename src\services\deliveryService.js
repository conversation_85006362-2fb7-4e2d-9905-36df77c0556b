// Delivery Service - Manages delivery data and operations
import databaseService from './databaseService.js';

const DELIVERY_STORAGE_KEY = 'inventory_deliveries';

class DeliveryService {
  constructor() {
    this.initializeDeliveries();
  }

  // Initialize delivery storage if it doesn't exist
  initializeDeliveries() {
    const existingData = localStorage.getItem(DELIVERY_STORAGE_KEY);
    if (!existingData) {
      const initialData = {
        deliveries: [],
        metadata: {
          lastUpdated: new Date().toISOString(),
          totalDeliveries: 0,
          nextDeliveryId: 1
        }
      };
      localStorage.setItem(DELIVERY_STORAGE_KEY, JSON.stringify(initialData));
    }
  }

  // Get all deliveries
  getAllDeliveries() {
    try {
      const data = JSON.parse(localStorage.getItem(DELIVERY_STORAGE_KEY));
      return data.deliveries || [];
    } catch (error) {
      console.error('Error reading deliveries:', error);
      return [];
    }
  }

  // Get delivery by ID
  getDeliveryById(deliveryId) {
    const deliveries = this.getAllDeliveries();
    return deliveries.find(delivery => delivery.id === deliveryId);
  }

  // Get deliveries by date range
  getDeliveriesByDateRange(startDate, endDate) {
    const deliveries = this.getAllDeliveries();
    return deliveries.filter(delivery => {
      const deliveryDate = new Date(delivery.date);
      return deliveryDate >= new Date(startDate) && deliveryDate <= new Date(endDate);
    });
  }

  // Get deliveries by date
  getDeliveriesByDate(date) {
    const deliveries = this.getAllDeliveries();
    const targetDate = new Date(date).toDateString();
    return deliveries.filter(delivery => 
      new Date(delivery.date).toDateString() === targetDate
    );
  }

  // Create new delivery
  createDelivery(deliveryData) {
    try {
      const data = JSON.parse(localStorage.getItem(DELIVERY_STORAGE_KEY));
      
      // Validate delivery data
      this.validateDeliveryData(deliveryData);

      // Create delivery object
      const newDelivery = {
        id: data.metadata.nextDeliveryId,
        date: deliveryData.date,
        customerInfo: deliveryData.customerInfo || {},
        items: deliveryData.items || [],
        status: deliveryData.status || 'dispatched',
        totalItems: deliveryData.items.reduce((sum, item) => sum + item.quantity, 0),
        notes: deliveryData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to deliveries array
      data.deliveries.push(newDelivery);
      
      // Update metadata
      data.metadata.nextDeliveryId++;
      data.metadata.totalDeliveries++;
      data.metadata.lastUpdated = new Date().toISOString();

      // Save to localStorage
      localStorage.setItem(DELIVERY_STORAGE_KEY, JSON.stringify(data));

      // Update inventory for dispatched items
      if (newDelivery.status === 'dispatched') {
        this.updateInventoryForDelivery(newDelivery.items, 'subtract');
      }

      return newDelivery;
    } catch (error) {
      console.error('Error creating delivery:', error);
      throw error;
    }
  }

  // Update delivery
  updateDelivery(deliveryId, updates) {
    try {
      const data = JSON.parse(localStorage.getItem(DELIVERY_STORAGE_KEY));
      const deliveryIndex = data.deliveries.findIndex(d => d.id === deliveryId);
      
      if (deliveryIndex === -1) {
        throw new Error('Delivery not found');
      }

      const oldDelivery = { ...data.deliveries[deliveryIndex] };
      const updatedDelivery = {
        ...oldDelivery,
        ...updates,
        updatedAt: new Date().toISOString()
      };

      // Handle status changes that affect inventory
      if (oldDelivery.status !== updatedDelivery.status) {
        this.handleStatusChange(oldDelivery, updatedDelivery);
      }

      data.deliveries[deliveryIndex] = updatedDelivery;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(DELIVERY_STORAGE_KEY, JSON.stringify(data));
      return updatedDelivery;
    } catch (error) {
      console.error('Error updating delivery:', error);
      throw error;
    }
  }

  // Delete delivery
  deleteDelivery(deliveryId) {
    try {
      const data = JSON.parse(localStorage.getItem(DELIVERY_STORAGE_KEY));
      const deliveryIndex = data.deliveries.findIndex(d => d.id === deliveryId);
      
      if (deliveryIndex === -1) {
        throw new Error('Delivery not found');
      }

      const delivery = data.deliveries[deliveryIndex];
      
      // Restore inventory if delivery was dispatched
      if (delivery.status === 'dispatched') {
        this.updateInventoryForDelivery(delivery.items, 'add');
      }

      data.deliveries.splice(deliveryIndex, 1);
      data.metadata.totalDeliveries--;
      data.metadata.lastUpdated = new Date().toISOString();

      localStorage.setItem(DELIVERY_STORAGE_KEY, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error deleting delivery:', error);
      throw error;
    }
  }

  // Update inventory based on delivery items
  updateInventoryForDelivery(items, operation) {
    items.forEach(item => {
      try {
        const product = databaseService.getProduct(item.productId);
        if (product) {
          let newQuantity;
          if (operation === 'subtract') {
            newQuantity = Math.max(0, product.quantity - item.quantity);
          } else if (operation === 'add') {
            newQuantity = product.quantity + item.quantity;
          }
          
          databaseService.updateProduct(item.productId, { quantity: newQuantity });
        }
      } catch (error) {
        console.error(`Error updating inventory for product ${item.productId}:`, error);
      }
    });
  }

  // Handle delivery status changes
  handleStatusChange(oldDelivery, newDelivery) {
    const oldStatus = oldDelivery.status;
    const newStatus = newDelivery.status;

    // If changing from dispatched to returned, add back to inventory
    if (oldStatus === 'dispatched' && newStatus === 'returned') {
      this.updateInventoryForDelivery(oldDelivery.items, 'add');
    }
    
    // If changing from returned to dispatched, subtract from inventory
    if (oldStatus === 'returned' && newStatus === 'dispatched') {
      this.updateInventoryForDelivery(newDelivery.items, 'subtract');
    }
  }

  // Validate delivery data
  validateDeliveryData(deliveryData) {
    if (!deliveryData.date) {
      throw new Error('Delivery date is required');
    }

    if (!deliveryData.items || deliveryData.items.length === 0) {
      throw new Error('At least one item is required for delivery');
    }

    // Validate each item
    deliveryData.items.forEach((item, index) => {
      if (!item.productId) {
        throw new Error(`Product ID is required for item ${index + 1}`);
      }
      if (!item.quantity || item.quantity <= 0) {
        throw new Error(`Valid quantity is required for item ${index + 1}`);
      }
      
      // Check if product exists and has sufficient stock
      const product = databaseService.getProduct(item.productId);
      if (!product) {
        throw new Error(`Product not found for item ${index + 1}`);
      }
      if (product.quantity < item.quantity) {
        throw new Error(`Insufficient stock for ${product.name}. Available: ${product.quantity}, Requested: ${item.quantity}`);
      }
    });
  }

  // Get delivery statistics
  getDeliveryStats() {
    const deliveries = this.getAllDeliveries();
    
    const stats = {
      totalDeliveries: deliveries.length,
      totalItemsDispatched: 0,
      deliveriesByStatus: {
        dispatched: 0,
        delivered: 0,
        returned: 0
      },
      deliveriesByDate: {},
      recentDeliveries: deliveries
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
    };

    deliveries.forEach(delivery => {
      stats.totalItemsDispatched += delivery.totalItems;
      stats.deliveriesByStatus[delivery.status]++;
      
      const dateKey = new Date(delivery.date).toDateString();
      if (!stats.deliveriesByDate[dateKey]) {
        stats.deliveriesByDate[dateKey] = 0;
      }
      stats.deliveriesByDate[dateKey]++;
    });

    return stats;
  }

  // Search deliveries
  searchDeliveries(query) {
    const deliveries = this.getAllDeliveries();
    const searchTerm = query.toLowerCase();
    
    return deliveries.filter(delivery => {
      return (
        delivery.customerInfo.name?.toLowerCase().includes(searchTerm) ||
        delivery.customerInfo.phone?.includes(searchTerm) ||
        delivery.notes?.toLowerCase().includes(searchTerm) ||
        delivery.items.some(item => 
          item.productName?.toLowerCase().includes(searchTerm)
        )
      );
    });
  }
}

const deliveryService = new DeliveryService();
export default deliveryService;
