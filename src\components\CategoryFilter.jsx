import React from 'react';
import { useCategories } from '../hooks/useProducts';
import './CategoryFilter.css';

const CategoryFilter = ({ selectedCategory, onCategoryChange }) => {
  const { categories } = useCategories();

  return (
    <div className="category-filter">
      <select
        value={selectedCategory}
        onChange={(e) => onCategoryChange(e.target.value)}
        className="category-select"
      >
        {categories.map(category => (
          <option key={category} value={category}>
            {category === 'All' ? 'All Categories' : category}
          </option>
        ))}
      </select>
    </div>
  );
};

export default CategoryFilter;
