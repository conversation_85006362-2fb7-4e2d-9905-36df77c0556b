/* Dashboard Styles */
.dashboard {
  width: 100%;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 2rem;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 2rem;
  font-weight: 700;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-card.total {
  border-left: 4px solid #667eea;
}

.stat-card.in-stock {
  border-left: 4px solid #28a745;
}

.stat-card.low-stock {
  border-left: 4px solid #ffc107;
}

.stat-card.out-of-stock {
  border-left: 4px solid #dc3545;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.dashboard-section {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dashboard-section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

/* Category List */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.category-item:hover {
  background: #e9ecef;
}

.category-name {
  font-weight: 500;
  color: #333;
}

.category-count {
  color: #666;
  font-size: 0.9rem;
}

/* Alert List */
.alert-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-radius: 5px;
  border-left: 4px solid #ffc107;
  background: #fff3cd;
}

.alert-item:has(.quantity-badge.out) {
  border-left-color: #dc3545;
  background: #f8d7da;
}

.alert-product {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.alert-product .product-name {
  font-weight: 500;
  color: #333;
}

.alert-product .product-category {
  font-size: 0.8rem;
  color: #666;
}

.quantity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.quantity-badge.low {
  background: #ffc107;
  color: #212529;
}

.quantity-badge.out {
  background: #dc3545;
  color: white;
}

/* Recent List */
.recent-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.recent-item:hover {
  background: #e9ecef;
}

.recent-product {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.recent-product .product-name {
  font-weight: 500;
  color: #333;
}

.recent-product .product-date {
  font-size: 0.8rem;
  color: #666;
}

.product-quantity {
  color: #667eea;
  font-weight: 500;
  font-size: 0.9rem;
}

/* Empty States */
.empty-message {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
  }
  
  .stat-icon {
    font-size: 2rem;
  }
  
  .dashboard-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .dashboard-section {
    padding: 1rem;
  }
  
  .alert-item,
  .recent-item,
  .category-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
